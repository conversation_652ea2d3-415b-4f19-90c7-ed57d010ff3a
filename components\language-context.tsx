"use client"

import { createContext, useContext, useState, type ReactNode } from "react"

type Language = "english" | "marathi" | "hindi"

interface LanguageContextType {
  language: Language
  setLanguage: (language: Language) => void
  t: (key: string) => string
}

const translations = {
  english: {
    // Navigation
    "nav.products": "Products",
    "nav.about": "About Us",
    "nav.values": "Our Values",
    "nav.contact": "Contact Us",

    // Home Page
    "home.hero.title": "Premium Quality Onions",
    "home.hero.subtitle": "From Farm to Table",
    "home.hero.description":
      "Cultivating excellence in every bulb. Fresh, organic, and sustainably grown onions for all your needs.",
    "home.hero.feature1": "Sun-Dried",
    "home.hero.feature2": "Organic",
    "home.hero.feature3": "Fresh",
    "home.stats.varieties": "Product Varieties",
    "home.stats.acres": "Acres Cultivated",
    "home.stats.experience": "Years Experience",
    "home.stats.quality": "Quality Assured",
    "home.products.title": "Our Premium Products",
    "home.products.description":
      "Discover our wide range of fresh and processed onion products, each carefully cultivated and processed to meet the highest quality standards.",

    // Product Details
    "product.back": "Back to Products",
    "product.perkg": "per kg",
    "product.season": "Season",
    "product.origin": "Origin",
    "product.language": "Select Language",
    "product.info": "Product Information",
    "product.sowing": "Sowing Information",
    "product.description": "Description",
    "product.specifications": "Specifications",
    "product.sowingTime": "Sowing Time",
    "product.soilType": "Soil Type",
    "product.spacing": "Spacing",
    "product.irrigation": "Irrigation",

    // About Page
    "about.title": "About Divya Seeds",
    "about.subtitle":
      "For over five decades, we have been cultivating excellence in onion farming, combining traditional wisdom with modern agricultural techniques.",
    "about.story.title": "Our Story",
    "about.journey.title": "Our Journey",
    "about.values.title": "Our Core Values",

    // Values Page
    "values.title": "Our Values",
    "values.subtitle":
      "Guided by our mission and vision, we strive to create a sustainable future for agriculture while delivering excellence in every harvest.",
    "values.mission.title": "Our Mission",
    "values.vision.title": "Our Vision",
    "values.principles.title": "Our Core Principles",
    "values.commitment.title": "Our Commitment",

    // Contact Page
    "contact.title": "Contact Us",
    "contact.subtitle":
      "Get in touch with us for inquiries, orders, or partnerships. We're here to help you with all your onion needs.",
    "contact.getintouch": "Get In Touch",
    "contact.location": "Our Location",
    "contact.phone": "Phone Numbers",
    "contact.email": "Email Addresses",
    "contact.hours": "Business Hours",
    "contact.findus": "Find Us",
    "contact.visit": "Visit Our Farm",
  },
  marathi: {
    // Navigation
    "nav.products": "उत्पादने",
    "nav.about": "आमच्याबद्दल",
    "nav.values": "आमची मूल्ये",
    "nav.contact": "संपर्क करा",

    // Home Page
    "home.hero.title": "उत्कृष्ट दर्जाचे कांदे",
    "home.hero.subtitle": "शेतापासून ते टेबलापर्यंत",
    "home.hero.description": "प्रत्येक कंदात उत्कृष्टता. ताजे, सेंद्रिय आणि शाश्वतपणे वाढवलेले कांदे तुमच्या सर्व गरजांसाठी.",
    "home.hero.feature1": "सूर्य-वाळवलेले",
    "home.hero.feature2": "सेंद्रिय",
    "home.hero.feature3": "ताजे",
    "home.stats.varieties": "उत्पादन प्रकार",
    "home.stats.acres": "लागवड केलेले एकर",
    "home.stats.experience": "वर्षांचा अनुभव",
    "home.stats.quality": "गुणवत्ता हमी",
    "home.products.title": "आमची प्रीमियम उत्पादने",
    "home.products.description":
      "आमच्या विविध प्रकारच्या ताज्या आणि प्रक्रिया केलेल्या कांदा उत्पादनांचा शोध घ्या, प्रत्येक काळजीपूर्वक लागवड केलेले आणि सर्वोच्च गुणवत्तेच्या मानकांना पूर्ण करण्यासाठी प्रक्रिया केलेले.",

    // Product Details
    "product.back": "उत्पादनांकडे परत",
    "product.perkg": "प्रति किलो",
    "product.season": "हंगाम",
    "product.origin": "उगम",
    "product.language": "भाषा निवडा",
    "product.info": "उत्पादन माहिती",
    "product.sowing": "पेरणी माहिती",
    "product.description": "वर्णन",
    "product.specifications": "वैशिष्ट्ये",
    "product.sowingTime": "पेरणीचा काळ",
    "product.soilType": "मातीचा प्रकार",
    "product.spacing": "अंतर",
    "product.irrigation": "सिंचन",

    // About Page
    "about.title": "दिव्य सीड्सबद्दल",
    "about.subtitle":
      "पाच दशकांहून अधिक काळ, आम्ही पारंपारिक ज्ञान आणि आधुनिक कृषी तंत्रज्ञानाचे संयोजन करून कांदा शेतीत उत्कृष्टता साधत आहोत.",
    "about.story.title": "आमची कहाणी",
    "about.journey.title": "आमचा प्रवास",
    "about.values.title": "आमची मूलभूत मूल्ये",

    // Values Page
    "values.title": "आमची मूल्ये",
    "values.subtitle":
      "आमच्या मिशन आणि व्हिजनद्वारे मार्गदर्शित, आम्ही प्रत्येक हंगामात उत्कृष्टता देत शेतीसाठी शाश्वत भविष्य निर्माण करण्याचा प्रयत्न करतो.",
    "values.mission.title": "आमचे मिशन",
    "values.vision.title": "आमचे व्हिजन",
    "values.principles.title": "आमची मूलभूत तत्त्वे",
    "values.commitment.title": "आमची वचनबद्धता",

    // Contact Page
    "contact.title": "संपर्क करा",
    "contact.subtitle": "चौकशी, ऑर्डर किंवा भागीदारीसाठी आमच्याशी संपर्क साधा. आम्ही तुमच्या सर्व कांदा गरजांसाठी येथे आहोत.",
    "contact.getintouch": "संपर्क साधा",
    "contact.location": "आमचे स्थान",
    "contact.phone": "फोन नंबर",
    "contact.email": "ईमेल पत्ते",
    "contact.hours": "व्यवसाय तास",
    "contact.findus": "आम्हाला शोधा",
    "contact.visit": "आमच्या शेतावर भेट द्या",
  },
  hindi: {
    // Navigation
    "nav.products": "उत्पाद",
    "nav.about": "हमारे बारे में",
    "nav.values": "हमारे मूल्य",
    "nav.contact": "संपर्क करें",

    // Home Page
    "home.hero.title": "प्रीमियम गुणवत्ता वाले प्याज",
    "home.hero.subtitle": "खेत से मेज तक",
    "home.hero.description": "हर बल्ब में उत्कृष्टता का खेती. ताजा, जैविक, और टिकाऊ रूप से उगाए गए प्याज आपकी सभी जरूरतों के लिए.",
    "home.hero.feature1": "सूरज-सूखे",
    "home.hero.feature2": "जैविक",
    "home.hero.feature3": "ताजा",
    "home.stats.varieties": "उत्पाद किस्में",
    "home.stats.acres": "खेती किए गए एकड़",
    "home.stats.experience": "वर्षों का अनुभव",
    "home.stats.quality": "गुणवत्ता आश्वासन",
    "home.products.title": "हमारे प्रीमियम उत्पाद",
    "home.products.description":
      "हमारे ताजे और प्रसंस्कृत प्याज उत्पादों की विस्तृत श्रृंखला का पता लगाएं, प्रत्येक को सावधानीपूर्वक उगाया और उच्चतम गुणवत्ता मानकों को पूरा करने के लिए प्रसंस्कृत किया गया है.",

    // Product Details
    "product.back": "उत्पादों पर वापस जाएं",
    "product.perkg": "प्रति किलो",
    "product.season": "मौसम",
    "product.origin": "उत्पत्ति",
    "product.language": "भाषा चुनें",
    "product.info": "उत्पाद जानकारी",
    "product.sowing": "बुवाई जानकारी",
    "product.description": "विवरण",
    "product.specifications": "विनिर्देश",
    "product.sowingTime": "बुवाई का समय",
    "product.soilType": "मिट्टी का प्रकार",
    "product.spacing": "अंतराल",
    "product.irrigation": "सिंचाई",

    // About Page
    "about.title": "दिव्य सीड्स के बारे में",
    "about.subtitle":
      "पांच दशकों से अधिक समय से, हम पारंपरिक ज्ञान और आधुनिक कृषि तकनीकों को जोड़कर प्याज की खेती में उत्कृष्टता हासिल कर रहे हैं.",
    "about.story.title": "हमारी कहानी",
    "about.journey.title": "हमारी यात्रा",
    "about.values.title": "हमारे मूल मूल्य",

    // Values Page
    "values.title": "हमारे मूल्य",
    "values.subtitle":
      "हमारे मिशन और विजन से निर्देशित, हम हर फसल में उत्कृष्टता प्रदान करते हुए कृषि के लिए एक स्थायी भविष्य बनाने का प्रयास करते हैं.",
    "values.mission.title": "हमारा मिशन",
    "values.vision.title": "हमारा विजन",
    "values.principles.title": "हमारे मूल सिद्धांत",
    "values.commitment.title": "हमारी प्रतिबद्धता",

    // Contact Page
    "contact.title": "संपर्क करें",
    "contact.subtitle": "पूछताछ, आदेश, या साझेदारी के लिए हमसे संपर्क करें. हम आपकी सभी प्याज की जरूरतों में मदद करने के लिए यहां हैं.",
    "contact.getintouch": "संपर्क करें",
    "contact.location": "हमारा स्थान",
    "contact.phone": "फोन नंबर",
    "contact.email": "ईमेल पते",
    "contact.hours": "व्यापार घंटे",
    "contact.findus": "हमें खोजें",
    "contact.visit": "हमारे खेत पर आएं",
  },
}

const LanguageContext = createContext<LanguageContextType>({
  language: "english",
  setLanguage: () => {},
  t: () => "",
})

export const useLanguage = () => useContext(LanguageContext)

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguage] = useState<Language>("english")

  const t = (key: string): string => {
    return translations[language][key as keyof (typeof translations)[typeof language]] || key
  }

  return <LanguageContext.Provider value={{ language, setLanguage, t }}>{children}</LanguageContext.Provider>
}
