# User Data Collection Setup

This document explains how to set up the user data collection dialog that appears when users scan QR codes for products.

## Features

- **User Data Dialog**: Collects Name, Address, District, Mobile Number, and Email
- **Google Sheets Integration**: Automatically saves user data to your Google Sheets
- **Form Validation**: Ensures all required fields are filled correctly
- **Toast Notifications**: Provides user feedback on form submission
- **Responsive Design**: Works on all device sizes

## Setup Instructions

### 1. Google Sheets Setup

1. Open your Google Sheets document: https://docs.google.com/spreadsheets/d/1v58oghosiqYCNBEwGsT8IfvxkL4FXSibNcxsWcFJ14Y/edit
2. Create a new sheet tab called "User Data" (or the script will create it automatically)
3. The columns will be: Name, Address, District, Contact, email, Timestamp

### 2. Google Apps Script Setup

1. Go to https://script.google.com/
2. Click "New Project"
3. Replace the default code with the content from `google-apps-script.js`
4. Update the `SPREADSHEET_ID` in the script with your Google Sheets ID
5. Save the project (give it a name like "User Data Collector")
6. Click "Deploy" > "New deployment"
7. Choose "Web app" as the type
8. Set execute as "Me" and access to "Anyone"
9. Click "Deploy"
10. Copy the web app URL

### 3. Environment Variables Setup

1. Create a `.env.local` file in your project root
2. Add the Google Apps Script URL:
   ```
   GOOGLE_SCRIPT_URL=https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec
   ```
3. Replace `YOUR_SCRIPT_ID` with the actual script ID from step 2

### 4. Testing

1. Start your development server: `npm run dev`
2. Navigate to any product page (e.g., `/product/1`)
3. The user data dialog should appear automatically
4. Fill out the form and submit
5. Check your Google Sheets to verify the data was saved

## How It Works

### User Flow

1. User scans QR code or visits product URL
2. User data dialog appears automatically
3. User fills out the required information
4. Data is validated on the client side
5. Data is submitted to Google Sheets via Google Apps Script
6. User sees success message and is redirected to product details
7. Dialog won't appear again for the same session

### Technical Implementation

- **Dialog Component**: `components/user-data-dialog.tsx`
- **API Route**: `app/api/submit-user-data/route.ts`
- **Google Apps Script**: `google-apps-script.js`
- **Integration**: Added to `app/product/[id]/page.tsx`

### Data Validation

- Name: Required, non-empty
- Address: Required, non-empty
- District: Required, non-empty
- Mobile: Required, exactly 10 digits
- Email: Required, valid email format

### Error Handling

- Client-side validation with toast notifications
- Server-side error handling
- Graceful fallback if Google Sheets is unavailable

## Customization

### Styling
The dialog uses your existing design system with orange theme colors. You can customize the appearance by modifying the Tailwind classes in `components/user-data-dialog.tsx`.

### Fields
To add or remove fields:
1. Update the `UserData` interface in `components/user-data-dialog.tsx`
2. Update the form fields in the dialog component
3. Update the Google Apps Script to handle the new fields
4. Update the validation logic

### Behavior
- To show the dialog on every visit, remove the session state logic
- To add a "Skip" option, add a skip button that closes the dialog
- To make fields optional, remove the `required` attributes and update validation

## Troubleshooting

### Dialog Not Appearing
- Check that `showUserDialog` state is set to `true`
- Verify the dialog component is properly imported

### Form Submission Fails
- Check the Google Apps Script URL in `.env.local`
- Verify the Google Apps Script is deployed with correct permissions
- Check browser console for error messages

### Data Not Saving to Sheets
- Verify the Google Sheets ID in the Apps Script
- Check that the script has permission to access the spreadsheet
- Test the Apps Script directly using the `testScript()` function

### Styling Issues
- Ensure all required UI components are installed
- Check that Tailwind CSS is properly configured
- Verify the Toaster component is added to the layout

## Security Notes

- The Google Apps Script runs with your permissions
- User data is transmitted over HTTPS
- Consider adding rate limiting for production use
- Validate data on both client and server sides
