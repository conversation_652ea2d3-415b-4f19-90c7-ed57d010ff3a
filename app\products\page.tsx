"use client"

import { Suspense, useEffect, useState } from "react"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import { Card, CardContent } from "@/components/ui/card"

// Simple product interface for the products page
interface SimpleProduct {
  id: string
  name: string
  // price: number
  season: string
  images: string[]
}

// Function to fetch products for the products page
async function fetchSimpleProducts(): Promise<SimpleProduct[]> {
  try {
    const SHEET_ID = "1l9-NuJYb9OITmVP_lhla39K3e2ODQMuItpJ8pNYbpB0"
    const url = `https://docs.google.com/spreadsheets/d/${SHEET_ID}/export?format=csv&gid=0`

    const response = await fetch(url, { cache: 'no-store' })
    if (!response.ok) {
      throw new Error(`Failed to fetch products: ${response.status}`)
    }

    const csvText = await response.text()
    return parseSimpleProducts(csvText)
  } catch (error) {
    console.error('Error fetching products:', error)
    return getFallbackSimpleProducts()
  }
}

function parseSimpleProducts(csvText: string): SimpleProduct[] {
  const lines = csvText.split('\n').filter(line => line.trim())
  if (lines.length < 2) return []

  const products: SimpleProduct[] = []

  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVLine(lines[i])

    // Only include products that have valid ID and Name
    if (values.length >= 3) {
      const id = values[1]?.trim()
      const name = values[2]?.trim()

      // Skip if ID or Name is empty, undefined, or just whitespace
      if (id && name && id !== '' && name !== '' && !isNaN(parseInt(id))) {
        // Extract season from Column D (index 3)
        const columnD = values[3]?.trim() || ""
        const firstFiveLetters = columnD.substring(0, 5).toLowerCase()

        let season = "Rabbi Season" // Default
        if (firstFiveLetters === "kharf" || firstFiveLetters === "khari") {
          season = "Kharif Season"
        } else if (firstFiveLetters === "rabbi" || firstFiveLetters === "rabbi ") {
          season = "Rabbi Season"
        }

        console.log(`Product: ${name}, Column D: "${columnD}", First 5: "${firstFiveLetters}", Season: ${season}`)

        products.push({
          id,
          name,
         // price: 450, // Default price
          season,
          images: ["/images/onion-seeds-placeholder.svg"]
        })
      }
    }
  }

  console.log(`✅ Found ${products.length} valid products out of ${lines.length - 1} total rows`)
  return products
}

function parseCSVLine(line: string): string[] {
  const result: string[] = []
  let current = ''
  let inQuotes = false

  for (let i = 0; i < line.length; i++) {
    const char = line[i]

    if (char === '"') {
      inQuotes = !inQuotes
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim().replace(/^"|"$/g, ''))
      current = ''
    } else {
      current += char
    }
  }

  result.push(current.trim().replace(/^"|"$/g, ''))
  return result
}

function getFallbackSimpleProducts(): SimpleProduct[] {
  return [
    {
      id: "1",
      name: "Amber Red Onion Seeds",
     // price: 450,
      season: "Rabbi Season",
      images: ["/images/onion-seeds-placeholder.svg"]
    },
    {
      id: "2",
      name: "Premium White Onion Seeds",
      // price: 500,
      season: "Rabbi Season",
      images: ["/images/onion-seeds-placeholder.svg"]
    }
  ]
}

function ProductGrid({ products }: { products: SimpleProduct[] }) {
  // Separate products by season
  const kharifProducts = products.filter(product =>
    product.season.toLowerCase().includes('kharif') ||
    product.season.toLowerCase().includes('kharf')
  )

  const rabiProducts = products.filter(product =>
    product.season.toLowerCase().includes('rabbi') ||
    product.season.toLowerCase().includes('rabi')
  )

  return (
    <div className="space-y-16">
      {/* Kharif Season Products */}
      {kharifProducts.length > 0 && (
        <div>
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">
              🌾 Kharif Season
            </h3>
            <p className="text-gray-600">
              Summer/Monsoon varieties (June - October)
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {kharifProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>
      )}

      {/* Rabbi Season Products */}
      {rabiProducts.length > 0 && (
        <div>
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">
              🌱 Rabbi Season
            </h3>
            <p className="text-gray-600">
              Winter varieties (October - March)
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {rabiProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>
      )}

      {/* If no products match either season, show all */}
      {kharifProducts.length === 0 && rabiProducts.length === 0 && products.length > 0 && (
        <div>
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">
              All Varieties
            </h3>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default function ProductsPage() {
  const [products, setProducts] = useState<SimpleProduct[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function loadProducts() {
      try {
        setLoading(true)
        setError(null)
        const fetchedProducts = await fetchSimpleProducts()
        setProducts(fetchedProducts)
      } catch (err) {
        console.error('Failed to load products:', err)
        setError('Failed to load products. Please try again later.')
      } finally {
        setLoading(false)
      }
    }

    loadProducts()
  }, [])

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="relative py-20 purple-pattern">
        <div className="absolute inset-0 purple-gradient opacity-90" />
        <div className="relative z-10 text-center text-white px-4 max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            Premium Quality Onion Seeds
            <span className="block text-purple-200">For Indian Agriculture</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-purple-100">
            Discover our comprehensive range of high-quality onion seeds designed specifically for Indian farming conditions
          </p>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Our Premium Seed Library</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Each seed in our collection is carefully curated and field-tested to ensure superior
                germination, exceptional yield, and resilience against disease. Grown from these premium
                seeds, our onions are known for their uniform size, rich flavor, longer shelf life, and
                adaptability to Indian growing conditions — making them a preferred choice for both
                farmers and markets alike          
            </p>
          </div>

          {error && (
            <div className="text-center mb-8">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-w-md mx-auto">
                <p className="text-red-600">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-2 text-red-700 underline hover:text-red-800"
                >
                  Try Again
                </button>
              </div>
            </div>
          )}

          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {Array.from({ length: 8 }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <div className="h-48 bg-gray-200" />
                  <CardContent className="p-4">
                    <div className="h-4 bg-gray-200 rounded mb-2" />
                    <div className="h-6 bg-gray-200 rounded" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : products.length > 0 ? (
            <ProductGrid products={products} />
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-600 text-lg">No products available at the moment.</p>
            </div>
          )}
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Why Choose Our Seeds?</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🌱</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">High Germination</h3>
              <p className="text-gray-600">90-95% germination rate guaranteed</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🛡️</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Disease Resistant</h3>
              <p className="text-gray-600">Resistant to common onion diseases</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🌾</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">High Yield</h3>
              <p className="text-gray-600">25-35 tons per hectare potential</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🇮🇳</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Indian Climate</h3>
              <p className="text-gray-600">Adapted for Indian conditions</p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
