'use client'

import { MessageCir<PERSON>, Users, Phone } from 'lucide-react'
import { Button } from '@/components/ui/button'

export default function FarmerSupport() {
  const handleWhatsAppCommunity = () => {
    window.open('https://chat.whatsapp.com/L5XeibLyx2hHFQViyFsI0j?mode=ac_t', '_blank')
  }

  const handleTechnicalSupport = () => {
    window.open('https://wa.me/************', '_blank')
  }

  return (
    <section className="py-16 bg-gradient-to-br from-purple-600 to-violet-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Farmer Support & Community
          </h2>
          <p className="text-xl text-purple-100 max-w-3xl mx-auto">
            Connect with fellow farmers and get expert guidance for successful farming
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* WhatsApp Community */}
          <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">
                Farmer Community
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Join our farmer community to turn every season into a success story. Connect with thousands of farmers, share experiences, and learn best practices.
              </p>
              <Button
                onClick={handleWhatsAppCommunity}
                className="bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-full font-semibold flex items-center justify-center mx-auto group"
              >
                <MessageCircle className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                Join WhatsApp Community
              </Button>
            </div>
          </div>

          {/* Technical Support */}
          <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Phone className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">
                Technical Support
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Get instant technical support and expert advice from our agricultural specialists. Available for consultation and problem-solving.
              </p>
              <Button
                onClick={handleTechnicalSupport}
                className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-full font-semibold flex items-center justify-center mx-auto group"
              >
                <Phone className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                WhatsApp: +91 88054 92999
              </Button>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-12 text-center">
          <div className="bg-white bg-opacity-10 rounded-xl p-6 max-w-2xl mx-auto">
            <h4 className="text-xl font-semibold text-white mb-3">
              🌾 Why Join Our Community?
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-purple-100">
              <div>
                <div className="text-2xl mb-2">📚</div>
                <p className="text-sm">Expert Tips & Guidance</p>
              </div>
              <div>
                <div className="text-2xl mb-2">🤝</div>
                <p className="text-sm">Farmer Network</p>
              </div>
              <div>
                <div className="text-2xl mb-2">📈</div>
                <p className="text-sm">Success Stories</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
