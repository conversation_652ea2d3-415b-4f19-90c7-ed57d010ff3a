"use client"

import { useState, useEffect } from "react"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import IndiaMap from "@/components/india-map"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Leaf, Sun, Droplets, Star, Quote, Users, TrendingUp, ArrowRight, CheckCircle } from "lucide-react"
import { AmberSeedSvg, WheatSvg, OnionSvg, OnionSliceSvg } from "@/components/onion-svg"
import { useTranslation } from "@/hooks/useTranslation"
import Link from "next/link"
import Image from "next/image"

// function ProductGrid() {
//   return (
//     <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
//       {productsData.map((product) => (
//         <ProductCard key={product.id} product={product} />
//       ))}
//     </div>
//   )
// }

function HeroSection() {
  const { t } = useTranslation()

  return (
    <section className="relative min-h-[80vh] flex items-center justify-center overflow-hidden purple-pattern">
      <div className="absolute inset-0 purple-gradient opacity-90" />
      <div className="relative z-10 text-center text-white px-4 max-w-6xl mx-auto">
        <div className="float-animation">
          <AmberSeedSvg className="h-32 w-32 mx-auto mb-8 text-purple-200" />
        </div>
        {/* Legacy Badge with Enhanced Animation */}
        <div className="mb-6 animate-fade-in-up">
          <div className="inline-flex items-center bg-white/20 backdrop-blur-sm border border-white/30 rounded-full px-6 py-3 hover:bg-white/30 transition-all duration-300 hover:scale-105">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
              <span className="text-lg md:text-xl font-semibold text-white animate-shimmer">
                32+ Years of Legacy and Trust
              </span>
              <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>

        <h1 className="text-5xl md:text-7xl font-bold mb-8 leading-tight animate-fade-in-up animation-delay-200">
          <span className="inline-block animate-slide-in-left">{t("hero.title")}</span>
          <span className="block text-purple-200 animate-slide-in-right animation-delay-400">{t("hero.subtitle")}</span>
        </h1>
        <p className="text-xl md:text-2xl mb-12 text-purple-100 max-w-4xl mx-auto animate-fade-in-up animation-delay-600">
          {t("hero.description")}
        </p>

        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12 animate-fade-in-up animation-delay-800">
          <Link href="/products">
            <Button size="lg" className="bg-white text-purple-600 hover:bg-purple-50 px-8 py-4 text-lg font-semibold transform hover:scale-105 transition-all duration-300 hover:shadow-xl group">
              {t("hero.cta1")} <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
            </Button>
          </Link>
          {/* <Link href="/contact">
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 px-8 py-4 text-lg font-semibold">
              {t("hero.cta2")}
            </Button>
          </Link> */}
        </div>
        {/* <div className="flex flex-wrap justify-center gap-4 text-sm md:text-base">
          <div className="flex items-center space-x-2 bg-white/20 px-4 py-2 rounded-full">
            <Sun className="h-5 w-5" />
            <span>Sun-Dried</span>
          </div>
          <div className="flex items-center space-x-2 bg-white/20 px-4 py-2 rounded-full">
            <Leaf className="h-5 w-5" />
            <span>Organic</span>
          </div>
          <div className="flex items-center space-x-2 bg-white/20 px-4 py-2 rounded-full">
            <Droplets className="h-5 w-5" />
            <span>Fresh</span>
          </div>
        </div> */}
      </div>

      {/* Decorative elements with enhanced animations */}
      <div className="absolute -bottom-16 -left-16 opacity-20 rotate-slow hover:opacity-30 transition-opacity duration-500 animate-bounce-slow">
        <WheatSvg className="w-48 h-48 text-purple-200" />
      </div>
      <div className="absolute -top-16 -right-16 opacity-20 rotate-slow hover:opacity-30 transition-opacity duration-500 animate-float-reverse">
        <AmberSeedSvg className="w-48 h-48 text-purple-200" />
      </div>

      {/* Additional floating particles */}
      <div className="absolute top-1/4 left-1/4 opacity-10 animate-float animation-delay-1000">
        <div className="w-4 h-4 bg-white rounded-full"></div>
      </div>
      <div className="absolute top-3/4 right-1/4 opacity-10 animate-float animation-delay-2000">
        <div className="w-6 h-6 bg-purple-200 rounded-full"></div>
      </div>
      <div className="absolute top-1/2 left-1/6 opacity-10 animate-float animation-delay-1500">
        <div className="w-3 h-3 bg-yellow-300 rounded-full"></div>
      </div>
    </section>
  )
}

function SeedAnimationSection() {
  return (
    <section className="py-16 bg-gradient-to-r from-green-50 to-purple-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            From Seed to Success
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Watch the successful journey of our farmers from various parts of India
          </p>
        </div>
        {/* <div className="flex justify-center">
          <div className="max-w-md mx-auto">
            <img
              src="/images/seedGif.gif"
              alt="Seed Growth Animation"
              className="w-full h-auto rounded-2xl shadow-lg"
            />
          </div>
        </div> */}
      </div>
    </section>
  )
}

function StatsSection() {
  const { t } = useTranslation()

  const stats = [
    { number: "27", label: t("stats.varieties"), icon: AmberSeedSvg },
    { number: "25,000+", label: t("stats.farmers"), icon: Leaf },
    { number: "32+", label: t("stats.experience"), icon: Sun },
    { number: "360+", label: t("stats.success"), icon: Droplets },
  ]

  return (
    <section className="py-12 bg-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <Card key={index} className={`text-center border-purple-200 hover:shadow-xl transition-all duration-500 hover:scale-110 hover:-translate-y-2 group animate-fade-in-up animation-delay-${(index + 1) * 200}`}>
              <CardContent className="p-8">
                <stat.icon className="h-12 w-12 text-purple-600 mx-auto mb-6 group-hover:scale-125 group-hover:text-purple-700 transition-all duration-300 animate-bounce-gentle" />
                <div className="text-4xl font-bold text-purple-800 mb-3 group-hover:text-purple-900 transition-colors duration-300 animate-count-up">{stat.number}</div>
                <div className="text-gray-600 font-medium group-hover:text-gray-700 transition-colors duration-300">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

function CompanyIntroSection() {
  const { t } = useTranslation()

  return (
    <section className="py-12 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              {t("about.title")}
            </h2>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              {t("about.subtitle")}
            </p>

            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-purple-600 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">Premium Quality Seeds</h3>
                  <p className="text-gray-600">High germination rate and disease-resistant varieties specially developed for Indian climate.</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-purple-600 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">Expert Guidance</h3>
                  <p className="text-gray-600">Comprehensive agricultural support from seed selection to harvest management.</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-purple-600 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">Sustainable Farming</h3>
                  <p className="text-gray-600">Promoting eco-friendly practices for long-term agricultural sustainability.</p>
                </div>
              </div>
            </div>

            <div className="mt-10">
              <Link href="/about">
                <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4">
                  Learn More About Us <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>

          <div className="relative w-full max-w-md mx-auto lg:mx-0">
            <div className="aspect-[3601/5401] bg-purple-100 rounded-2xl overflow-hidden">
              <img
                src="/images/amberBanner.jpg"
                alt="Indian Farmer"
                className="w-full h-full object-cover"
              />
            </div>
            {/* <div className="absolute top-4 left-4 bg-white p-3 sm:p-4 rounded-xl shadow-lg max-w-[calc(100%-2rem)]">
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-purple-600">32+</div>
                <div className="text-xs sm:text-sm text-gray-600">Years of Excellence</div>
              </div>
            </div> */}
          </div>
        </div>
      </div>
    </section>
  )
}


interface VideoData {
  id: string
  youtubeLink: string
  videoId: string
  thumbnail: string
  title: string
}

function TestimonialsSection() {
  const [videos, setVideos] = useState<VideoData[]>([])
  const [selectedVideo, setSelectedVideo] = useState<VideoData | null>(null)
  const [currentIndex, setCurrentIndex] = useState(0)

  // Fetch YouTube videos from Google Sheets
  useEffect(() => {
    const fetchVideos = async () => {
      try {
        const SHEET_ID = "1l9-NuJYb9OITmVP_lhla39K3e2ODQMuItpJ8pNYbpB0"
        const GID = "1327562670"
        const url = `https://docs.google.com/spreadsheets/d/${SHEET_ID}/export?format=csv&gid=${GID}`

        const response = await fetch(url, { cache: 'no-store' })
        const csvText = await response.text()

        const rows = csvText.split('\n').slice(1) // Skip header row
        const videoData = rows
          .filter(row => row.trim())
          .map(row => {
            const [id, youtubeLink, title] = row.split(',').map(cell => cell.trim().replace(/"/g, ''))
            if (id && youtubeLink) {
              // Extract video ID from YouTube URL
              const videoId = youtubeLink.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)?.[1]
              if (videoId) {
                return {
                  id,
                  youtubeLink,
                  videoId,
                  thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
                  title: title || `Farmer Success Story #${id}`
                }
              }
            }
            return null
          })
          .filter((item): item is VideoData => item !== null)

        setVideos(videoData)
      } catch (error) {
        console.error('Error fetching videos:', error)
      }
    }

    fetchVideos()
  }, [])

  const getVisibleVideos = () => {
    if (typeof window !== 'undefined') {
      if (window.innerWidth < 768) return 1 // Mobile: 1 video
      if (window.innerWidth < 1024) return 2 // Tablet: 2 videos
      return 3 // Desktop: 3 videos
    }
    return 3 // Default for SSR
  }

  const [visibleVideos, setVisibleVideos] = useState(getVisibleVideos)

  useEffect(() => {
    const handleResize = () => {
      setVisibleVideos(getVisibleVideos())
      setCurrentIndex(0) // Reset to start when screen size changes
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const nextSlide = () => {
    if (videos.length > visibleVideos) {
      setCurrentIndex((prev) => Math.min(prev + 1, videos.length - visibleVideos))
    }
  }

  const prevSlide = () => {
    setCurrentIndex((prev) => Math.max(prev - 1, 0))
  }

  const canGoNext = videos.length > visibleVideos && currentIndex < videos.length - visibleVideos
  const canGoPrev = currentIndex > 0

  const openVideoModal = (video: VideoData) => {
    setSelectedVideo(video)
  }

  const closeVideoModal = () => {
    setSelectedVideo(null)
  }

  return (
    <section className="py-12 bg-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">From Seed to Success</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Watch the successful journey of our farmers from various parts of India
          </p>
        </div>

        {videos.length > 0 && (
          <div className="relative">
            {/* Video Carousel */}
            <div className="flex justify-center items-center space-x-2 md:space-x-4">
              <button
                onClick={prevSlide}
                disabled={!canGoPrev}
                className={`p-2 md:p-3 rounded-full transition-colors z-10 ${
                  canGoPrev
                    ? 'bg-purple-600 text-white hover:bg-purple-700 active:bg-purple-800'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <ArrowRight className="h-5 w-5 md:h-6 md:w-6 rotate-180" />
              </button>

              <div className="overflow-hidden w-full max-w-sm md:max-w-2xl lg:max-w-6xl">
                <div
                  className="flex space-x-4 md:space-x-6 transition-transform duration-300"
                  style={{
                    transform: `translateX(-${currentIndex * (visibleVideos === 1 ? 280 : visibleVideos === 2 ? 320 : 320)}px)`
                  }}
                >
                  {videos.map((video: VideoData) => (
                    <div
                      key={video.id}
                      className="flex-shrink-0 w-72 md:w-80 cursor-pointer group"
                      onClick={() => openVideoModal(video)}
                    >
                      <div className="relative rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                        <img
                          src={video.thumbnail}
                          alt={`Video ${video.id}`}
                          className="w-full h-40 md:h-48 object-cover"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group-hover:bg-opacity-20 transition-all duration-300">
                          <div className="w-12 h-12 md:w-16 md:h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <div className="w-0 h-0 border-l-[8px] md:border-l-[12px] border-l-purple-600 border-t-[6px] md:border-t-[8px] border-t-transparent border-b-[6px] md:border-b-[8px] border-b-transparent ml-1"></div>
                          </div>
                        </div>
                      </div>
                      <div className="mt-3 md:mt-4 text-center">
                        <p className="text-gray-700 font-medium text-sm md:text-base">{video.title}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <button
                onClick={nextSlide}
                disabled={!canGoNext}
                className={`p-2 md:p-3 rounded-full transition-colors z-10 ${
                  canGoNext
                    ? 'bg-purple-600 text-white hover:bg-purple-700 active:bg-purple-800'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <ArrowRight className="h-5 w-5 md:h-6 md:w-6" />
              </button>
            </div>
          </div>
        )}

        {/* Video Modal */}
        {selectedVideo && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="relative bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <button
                onClick={closeVideoModal}
                className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all"
              >
                ✕
              </button>
              <div className="aspect-video">
                <iframe
                  src={`https://www.youtube.com/embed/${selectedVideo.videoId}?autoplay=1`}
                  title="YouTube video player"
                  style={{ border: 0 }}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  className="w-full h-full"
                ></iframe>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}

function AgriculturalTipsSection() {
  const { t } = useTranslation()

  const tips = [
    {
      icon: Droplets,
      title: t("tips.irrigation"),
      description: "Proper water management for onion crop. Use drip irrigation system for optimal results."
    },
    {
      icon: Leaf,
      title: t("tips.soil"),
      description: "Choose well-drained loamy soil. Maintain pH 6.0-7.5 for best growth conditions."
    },
    {
      icon: Sun,
      title: t("tips.seasonal"),
      description: "Plant during Rabi season in October-December for optimal yield and quality."
    },
    {
      icon: TrendingUp,
      title: t("tips.yield"),
      description: "Increase yield with quality seeds and proper spacing techniques."
    }
  ]

  return (
    <section className="py-12 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">{t("tips.title")}</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t("tips.subtitle")}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {tips.map((tip, index) => (
            <Card key={index} className="text-center border-purple-200 hover:shadow-xl transition-all duration-300 hover:scale-105">
              <CardContent className="p-8">
                <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <tip.icon className="h-10 w-10 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">{tip.title}</h3>
                <p className="text-gray-600 leading-relaxed">{tip.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link href="/contact">
            <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4">
              Get Expert Consultation <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default function HomePage() {
  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      <HeroSection />

      {/* <SeedAnimationSection /> */}

      <StatsSection />

      <CompanyIntroSection />

      <TestimonialsSection />

      <AgriculturalTipsSection />

      <IndiaMap />

      <Footer />
    </div>
  )
}
