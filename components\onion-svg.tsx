export function AmberSeedSvg({ className = "w-6 h-6" }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className={className} fill="currentColor">
      <path d="M12 2L8 6h8l-4-4z" />
      <path d="M8 6v4c0 2.2 1.8 4 4 4s4-1.8 4-4V6H8z" />
      <path d="M10 14v6c0 1.1.9 2 2 2s2-.9 2-2v-6h-4z" />
      <circle cx="12" cy="8" r="1" fill="white" opacity="0.8" />
    </svg>
  )
}

export function WheatSvg({ className = "w-6 h-6" }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className={className} fill="currentColor">
      <path d="M12 22V2" stroke="currentColor" strokeWidth="2" fill="none" />
      <path d="M8 4l4-2 4 2" stroke="currentColor" strokeWidth="1.5" fill="none" />
      <path d="M8 6l4-2 4 2" stroke="currentColor" strokeWidth="1.5" fill="none" />
      <path d="M8 8l4-2 4 2" stroke="currentColor" strokeWidth="1.5" fill="none" />
      <path d="M8 10l4-2 4 2" stroke="currentColor" strokeWidth="1.5" fill="none" />
      <path d="M8 12l4-2 4 2" stroke="currentColor" strokeWidth="1.5" fill="none" />
      <path d="M8 14l4-2 4 2" stroke="currentColor" strokeWidth="1.5" fill="none" />
    </svg>
  )
}

export function OnionSvg({ className = "w-24 h-24" }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className={className} fill="currentColor">
      <path
        d="M256 96c-33.8 0-65.5 8.7-93 24-27.5 15.3-49.6 37.3-64.9 64.9-15.3 27.5-24 59.2-24 93 0 33.8 8.7 65.5 24 93 15.3 27.5 37.3 49.6 64.9 64.9 27.5 15.3 59.2 24 93 24s65.5-8.7 93-24c27.5-15.3 49.6-37.3 64.9-64.9 15.3-27.5 24-59.2 24-93 0-33.8-8.7-65.5-24-93-15.3-27.5-37.3-49.6-64.9-64.9-27.5-15.3-59.2-24-93-24zm0 32c26.5 0 51.3 6.8 72.8 18.8 21.5 12 38.8 29.3 50.8 50.8 12 21.5 18.8 46.3 18.8 72.8 0 26.5-6.8 51.3-18.8 72.8-12 21.5-29.3 38.8-50.8 50.8-21.5 12-46.3 18.8-72.8 18.8-26.5 0-51.3-6.8-72.8-18.8-21.5-12-38.8-29.3-50.8-50.8-12-21.5-18.8-46.3-18.8-72.8 0-26.5 6.8-51.3 18.8-72.8 12-21.5 29.3-38.8 50.8-50.8 21.5-12 46.3-18.8 72.8-18.8z"
        opacity="0.4"
      />
      <path
        d="M256 64c-8.8 0-16-7.2-16-16V16c0-8.8 7.2-16 16-16s16 7.2 16 16v32c0 8.8-7.2 16-16 16zm0 384c-8.8 0-16-7.2-16-16v-32c0-8.8 7.2-16 16-16s16 7.2 16 16v32c0 8.8-7.2 16-16 16z"
        opacity="0.6"
      />
    </svg>
  )
}

export function OnionSliceSvg({ className = "w-24 h-24" }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className={className} fill="currentColor">
      <path
        d="M256 64c-106 0-192 86-192 192s86 192 192 192 192-86 192-192S362 64 256 64zm0 32c88.4 0 160 71.6 160 160s-71.6 160-160 160S96 344.4 96 256 167.6 96 256 96z"
        opacity="0.4"
      />
      <path
        d="M256 128c-70.7 0-128 57.3-128 128s57.3 128 128 128 128-57.3 128-128-57.3-128-128-128zm0 32c53 0 96 43 96 96s-43 96-96 96-96-43-96-96 43-96 96-96z"
        opacity="0.6"
      />
      <path
        d="M256 192c-35.3 0-64 28.7-64 64s28.7 64 64 64 64-28.7 64-64-28.7-64-64-64zm0 32c17.7 0 32 14.3 32 32s-14.3 32-32 32-32-14.3-32-32 14.3-32 32-32z"
        opacity="0.8"
      />
    </svg>
  )
}
