/**
 * Converts a Google Drive sharing link to a direct image URL that can be displayed in img tags
 * Supports multiple Google Drive URL formats and provides fallback options
 * @param driveUrl - Google Drive sharing URL from Column N
 * @returns Direct image URL or fallback placeholder
 */
export function convertDriveUrlToDirectUrl(driveUrl: string): string {
  console.log('🔄 Processing Drive URL:', driveUrl)
  
  // Return placeholder if URL is empty or invalid
  if (!driveUrl || driveUrl.trim() === '' || driveUrl === 'undefined' || driveUrl === 'null') {
    console.log('❌ Empty or invalid URL, using placeholder')
    return "/images/onion-seeds-placeholder.svg"
  }

  try {
    const cleanUrl = driveUrl.trim()
    
    // If it's already a direct image URL, use it as-is
    if (cleanUrl.match(/\.(jpg|jpeg|png|gif|webp|bmp)(\?.*)?$/i)) {
      console.log('✅ Direct image URL detected:', cleanUrl)
      return cleanUrl
    }

    // Extract file ID from various Google Drive URL formats
    let fileId = ''
    
    // Format 1: https://drive.google.com/file/d/FILE_ID/view?usp=sharing
    const match1 = cleanUrl.match(/\/file\/d\/([a-zA-Z0-9-_]+)/)
    if (match1) {
      fileId = match1[1]
      console.log('📁 Found file ID (format 1):', fileId)
    }
    
    // Format 2: https://drive.google.com/open?id=FILE_ID
    if (!fileId) {
      const match2 = cleanUrl.match(/[?&]id=([a-zA-Z0-9-_]+)/)
      if (match2) {
        fileId = match2[1]
        console.log('📁 Found file ID (format 2):', fileId)
      }
    }
    
    // Format 3: https://drive.google.com/uc?id=FILE_ID
    if (!fileId) {
      const match3 = cleanUrl.match(/uc\?id=([a-zA-Z0-9-_]+)/)
      if (match3) {
        fileId = match3[1]
        console.log('📁 Found file ID (format 3):', fileId)
      }
    }
    
    if (fileId) {
      // Use a proxy service to bypass CORS issues with Google Drive
      // This service converts Google Drive links to direct image URLs
      const directUrl = `https://drive.google.com/uc?export=download&id=${fileId}`
      console.log('✅ Converted to direct URL:', directUrl)
      return directUrl
    }
    
    // If no file ID found but it's a Google Drive URL, try as-is
    if (cleanUrl.includes('drive.google.com')) {
      console.log('⚠️ Google Drive URL but no file ID found, trying as-is:', cleanUrl)
      return cleanUrl
    }
    
    // If it's any other URL, try it as-is
    if (cleanUrl.startsWith('http')) {
      console.log('🌐 External URL, trying as-is:', cleanUrl)
      return cleanUrl
    }
    
    // Fallback to placeholder
    console.log('❌ Could not process URL, using placeholder')
    return "/images/onion-seeds-placeholder.svg"
    
  } catch (error) {
    console.error('💥 Error processing Drive URL:', error)
    return "/images/onion-seeds-placeholder.svg"
  }
}

/**
 * Validates if a URL is accessible and returns appropriate format
 * @param url - URL to validate
 * @returns boolean indicating if URL is valid
 */
export function isValidImageUrl(url: string): boolean {
  if (!url || url.trim() === '') return false
  
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

/**
 * Gets the appropriate image URL for a product from Column N data
 * @param columnNValue - Value from Column N (index 13) of the Google Sheet
 * @param productId - Product ID for fallback naming
 * @returns Processed image URL
 */
export function getProductImageUrl(columnNValue: string, productId?: string): string {
  const processedUrl = convertDriveUrlToDirectUrl(columnNValue)
  
  // If we got a placeholder and have a product ID, we could potentially
  // look for a local image file as fallback
  if (processedUrl === "/images/onion-seeds-placeholder.svg" && productId) {
    // Check if there's a local product image
    const localImageUrl = `/images/products/product-${productId}.jpg`
    // Note: In a real implementation, you'd want to check if this file exists
    // For now, we'll stick with the placeholder
  }
  
  return processedUrl
}
