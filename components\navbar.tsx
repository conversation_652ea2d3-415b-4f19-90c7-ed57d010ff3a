"use client"

import { useState } from "react"
import Link from "next/link"
import { Menu, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useTranslation } from "@/hooks/useTranslation"
import { useLanguage } from "@/components/language-context"
import { AmberSeedSvg } from "@/components/onion-svg"
import Image from "next/image"

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const { t, language } = useTranslation()
  const { setLanguage } = useLanguage()

  return (
    <nav className="bg-white/95 backdrop-blur-sm border-b border-purple-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <Image
              src="/images/amber logo-01.png"
              alt="Amber Seeds & Farms Logo"
              width={84}
              height={84}
              className="object-contain"
            />
            <span className="text-lg font-bold text-purple-800">Amber Seeds & Farms</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-700 hover:text-purple-600 transition-colors font-medium">
              {t("nav.home")}
            </Link>
            <Link href="/products" className="text-gray-700 hover:text-purple-600 transition-colors font-medium">
              {t("nav.products")}
            </Link>
            <Link href="/about" className="text-gray-700 hover:text-purple-600 transition-colors font-medium">
              {t("nav.about")}
            </Link>
            <Link href="/values" className="text-gray-700 hover:text-purple-600 transition-colors font-medium">
              {t("nav.values")}
            </Link>
            <Link href="/contact" className="text-gray-700 hover:text-purple-600 transition-colors font-medium">
              {t("nav.contact")}
            </Link>

            {/* Language Selector */}
            {/* <div className="flex space-x-2">
              <Button
                variant={language === "english" ? "default" : "outline"}
                size="sm"
                onClick={() => setLanguage("english")}
                className={
                  language === "english"
                    ? "bg-purple-600 hover:bg-purple-700"
                    : "border-purple-300 text-purple-600 hover:bg-purple-50"
                }
              >
                English
              </Button>
              <Button
                variant={language === "marathi" ? "default" : "outline"}
                size="sm"
                onClick={() => setLanguage("marathi")}
                className={
                  language === "marathi"
                    ? "bg-purple-600 hover:bg-purple-700"
                    : "border-purple-300 text-purple-600 hover:bg-purple-50"
                }
              >
                मराठी
              </Button>
              <Button
                variant={language === "hindi" ? "default" : "outline"}
                size="sm"
                onClick={() => setLanguage("hindi")}
                className={
                  language === "hindi"
                    ? "bg-purple-600 hover:bg-purple-700"
                    : "border-purple-300 text-purple-600 hover:bg-purple-50"
                }
              >
                हिंदी
              </Button>
            </div> */}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={() => setIsOpen(!isOpen)} className="text-gray-700">
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-purple-100">
              <Link
                href="/"
                className="block px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors font-medium"
                onClick={() => setIsOpen(false)}
              >
                {t("nav.home")}
              </Link>
              <Link
                href="/products"
                className="block px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors font-medium"
                onClick={() => setIsOpen(false)}
              >
                {t("nav.products")}
              </Link>
              <Link
                href="/about"
                className="block px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors font-medium"
                onClick={() => setIsOpen(false)}
              >
                {t("nav.about")}
              </Link>
              <Link
                href="/values"
                className="block px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors font-medium"
                onClick={() => setIsOpen(false)}
              >
                {t("nav.values")}
              </Link>
              <Link
                href="/contact"
                className="block px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors font-medium"
                onClick={() => setIsOpen(false)}
              >
                {t("nav.contact")}
              </Link>

              {/* Mobile Language Selector - Removed */}
              {/* <div className="flex space-x-2 px-3 py-2">
                <Button
                  variant={language === "english" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setLanguage("english")}
                  className={
                    language === "english"
                      ? "bg-purple-600 hover:bg-purple-700"
                      : "border-purple-300 text-purple-600 hover:bg-purple-50"
                  }
                >
                  English
                </Button>
                <Button
                  variant={language === "marathi" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setLanguage("marathi")}
                  className={
                    language === "marathi"
                      ? "bg-purple-600 hover:bg-purple-700"
                      : "border-purple-300 text-purple-600 hover:bg-purple-50"
                  }
                >
                  मराठी
                </Button>
                <Button
                  variant={language === "hindi" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setLanguage("hindi")}
                  className={
                    language === "hindi"
                      ? "bg-purple-600 hover:bg-purple-700"
                      : "border-purple-300 text-purple-600 hover:bg-purple-50"
                  }
                >
                  हिंदी
                </Button>
              </div> */}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
