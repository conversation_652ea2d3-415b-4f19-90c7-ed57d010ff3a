"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { User, MapPin, Phone, Mail, Building } from "lucide-react"
import { toast } from "sonner"

interface UserData {
  name: string
  address: string
  district: string
  mobile: string
  email: string
}

interface UserDataDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (userData: UserData) => void
}

export default function UserDataDialog({ isOpen, onClose, onSubmit }: UserDataDialogProps) {
  const [formData, setFormData] = useState<UserData>({
    name: "",
    address: "",
    district: "",
    mobile: "",
    email: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (field: keyof UserData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error("Please enter your name")
      return false
    }
    if (!formData.address.trim()) {
      toast.error("Please enter your address")
      return false
    }
    if (!formData.district.trim()) {
      toast.error("Please enter your district")
      return false
    }
    if (!formData.mobile.trim()) {
      toast.error("Please enter your mobile number")
      return false
    }
    if (!/^\d{10}$/.test(formData.mobile)) {
      toast.error("Please enter a valid 10-digit mobile number")
      return false
    }
    
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // Google Form submission URL
      const FORM_ID = '1FAIpQLSfF4jSvHA-4ejN2u0Jf29KrFjMOZlT_dlaiVEJPvnsRybInqw';
      
      // Entry IDs extracted from your pre-filled URL
      const NAME_ENTRY_ID = '1818854567';
      const ADDRESS_ENTRY_ID = '1090437100'; // Note: This appears to be used for district in your URL
      const DISTRICT_ENTRY_ID = ''; // This field seems to be missing from your pre-filled URL
      const CONTACT_ENTRY_ID = '2033410710';
      const EMAIL_ENTRY_ID = '940851548';
      
      // Build the form submission URL with URL parameters
      const formUrl = new URL(`https://docs.google.com/forms/d/e/${FORM_ID}/formResponse`);
      
      // Add form data as URL parameters
      formUrl.searchParams.append(`entry.${NAME_ENTRY_ID}`, formData.name);
      
      // It looks like your form might have a different structure than your component
      // Based on your URL, it seems the address field might be missing or combined with district
      // I'm using the address entry ID for the address field, but you may need to adjust this
      formUrl.searchParams.append(`entry.${ADDRESS_ENTRY_ID}`, formData.address);
      
      // If you have a separate district field in your form, uncomment this line and add the correct entry ID
      // formUrl.searchParams.append(`entry.${DISTRICT_ENTRY_ID}`, formData.district);
      
      formUrl.searchParams.append(`entry.${CONTACT_ENTRY_ID}`, formData.mobile);
      formUrl.searchParams.append(`entry.${EMAIL_ENTRY_ID}`, formData.email);
      
      console.log("Submitting to Google Form:", formUrl.toString());
      
      // Use a hidden iframe to submit the form without navigating away
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      document.body.appendChild(iframe);
      
      // Set up a listener to know when the iframe has loaded
      iframe.onload = () => {
        document.body.removeChild(iframe);
        
        toast.success("Data submitted successfully!");
        onSubmit(formData);
        onClose();
        
        // Reset form
        setFormData({
          name: "",
          address: "",
          district: "",
          mobile: "",
          email: "",
        });
        
        setIsSubmitting(false);
      };
      
      // Submit the form by setting the iframe's src to the form URL
      iframe.src = formUrl.toString();
      
    } catch (error) {
      console.error('Error submitting data:', error);
      toast.error("Failed to submit data. Please try again.");
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-orange-800 flex items-center gap-2">
            <User className="h-6 w-6" />
            Welcome to Divya Seeds
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Please provide your details to access product information and receive updates about our premium onion varieties.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 mt-6">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <User className="h-4 w-4 text-orange-600" />
              आपका नाम *
            </Label>
            <Input
              id="name"
              type="text"
              placeholder="अपना नाम दर्ज करें"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className="border-orange-200 focus:border-orange-500 focus:ring-orange-500"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="address" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <MapPin className="h-4 w-4 text-orange-600" />
               पता
            </Label>
            <Textarea
              id="address"
              placeholder="अपना पता दर्ज करें"
              value={formData.address}
              onChange={(e) => handleInputChange("address", e.target.value)}
              className="border-orange-200 focus:border-orange-500 focus:ring-orange-500 min-h-[80px]"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="district" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Building className="h-4 w-4 text-orange-600" />
              ज़िला *
            </Label>
            <Input
              id="district"
              type="text"
              placeholder="अपना जिला दर्ज करें"
              value={formData.district}
              onChange={(e) => handleInputChange("district", e.target.value)}
              className="border-orange-200 focus:border-orange-500 focus:ring-orange-500"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="mobile" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Phone className="h-4 w-4 text-orange-600" />
              मोबाइल नंबर *
            </Label>
            <Input
              id="mobile"
              type="tel"
              placeholder="अपना 10 अंकों का मोबाइल नंबर दर्ज करें"
              value={formData.mobile}
              onChange={(e) => handleInputChange("mobile", e.target.value.replace(/\D/g, '').slice(0, 10))}
              className="border-orange-200 focus:border-orange-500 focus:ring-orange-500"
              required
            />
          </div>

          {/* <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Mail className="h-4 w-4 text-orange-600" />
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email address"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              className="border-orange-200 focus:border-orange-500 focus:ring-orange-500"
              required
            />
          </div> */}

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1 border-orange-200 text-orange-700 hover:bg-orange-50"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Submitting..." : "Submit & Continue"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}







