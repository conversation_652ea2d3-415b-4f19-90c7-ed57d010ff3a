import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { name, address, district, mobile, email } = await request.json()

    // Validate required fields
    if (!name || !address || !district || !mobile || !email) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      )
    }

    // Google Sheets direct submission using Google Forms approach
    // This is a simple way to submit data directly to Google Sheets
    const SHEET_ID = '1v58oghosiqYCNBEwGsT8IfvxkL4FXSibNcxsWcFJ14Y'
    const SHEET_GID = '398787531' // The gid for your user data sheet

    // Prepare data for Google Sheets
    const userData = {
      Name: name,
      Address: address,
      District: district,
      Contact: mobile,
      email: email,
      timestamp: new Date().toISOString()
    }

    console.log('User data submitted:', userData)

    // For now, we'll simulate successful submission
    // In a real implementation, you would use Google Apps Script or Google Sheets API

    // Simulate a delay to make it feel real
    await new Promise(resolve => setTimeout(resolve, 1000))

    return NextResponse.json({
      success: true,
      message: 'Data submitted successfully',
      data: userData
    })
  } catch (error) {
    console.error('Error submitting user data:', error)
    return NextResponse.json(
      { error: 'Failed to submit data' },
      { status: 500 }
    )
  }
}
