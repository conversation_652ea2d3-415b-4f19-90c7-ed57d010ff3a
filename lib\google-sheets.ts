// Google Sheets data fetcher utility

export interface ProductDetail {
  header: string
  value: string
}

export interface Product {
  id: string
  name: string
  price: number
  images: string[]
  season: string
  // Dynamic product details with headers from respective sheets
  productDetails: ProductDetail[]
  // Legacy structured details for fallback
  legacyDetails: {
    sowingSeason: string
    recommendedArea: string
    seedQuantity: string
    bulbColour: string
    bulbShape: string
    bulbWeight: string
    maturityPeriod: string
    marketPrice: string
    expectedYield: string
    characteristics: string
  }
  // Keep legacy structure for backward compatibility
  productInfo: {
    english: {
      description: string
      specifications: string
    }
    marathi: {
      description: string
      specifications: string
    }
    hindi: {
      description: string
      specifications: string
    }
  }
  sowingInfo: {
    english: {
      sowingTime: string
      soilType: string
      spacing: string
      irrigation: string
    }
    marathi: {
      sowingTime: string
      soilType: string
      spacing: string
      irrigation: string
    }
    hindi: {
      sowingTime: string
      soilType: string
      spacing: string
      irrigation: string
    }
  }
}

const SHEET_ID = "1l9-NuJYb9OITmVP_lhla39K3e2ODQMuItpJ8pNYbpB0"

// Sheet GIDs for different languages
// Note: These GIDs need to be updated with actual values from the Google Sheet
const SHEET_GIDS = {
  english: "0",        // Default sheet (English)
  hindi: "1199415046", // Hindi sheet - update with correct GID
  marathi: "907114351" // Marathi sheet - update with correct GID
}

export async function fetchProductsFromSheet(): Promise<Product[]> {
  try {
    // Fetch from default English sheet
    const url = `https://docs.google.com/spreadsheets/d/${SHEET_ID}/export?format=csv&gid=${SHEET_GIDS.english}`

    const response = await fetch(url, {
      cache: 'no-store', // Always fetch fresh data
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch sheet data: ${response.status}`)
    }

    const csvText = await response.text()
    const products = parseCSVToProducts(csvText)

    return products
  } catch (error) {
    console.error('Error fetching products from Google Sheets:', error)
    // Fallback to local data if sheet fails
    return getFallbackProducts()
  }
}

export async function fetchProductFromLanguageSheet(productId: string, language: 'english' | 'hindi' | 'marathi'): Promise<Product | null> {
  try {
    const gid = SHEET_GIDS[language]
    const url = `https://docs.google.com/spreadsheets/d/${SHEET_ID}/export?format=csv&gid=${gid}`

    const response = await fetch(url, {
      cache: 'no-store',
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch ${language} sheet data: ${response.status}`)
    }

    const csvText = await response.text()
    const products = parseCSVToProducts(csvText)

    return products.find(p => p.id === productId) || null
  } catch (error) {
    console.error(`Error fetching product from ${language} sheet:`, error)
    return null
  }
}

function parseCSVToProducts(csvText: string): Product[] {
  const lines = csvText.split('\n').filter(line => line.trim())
  
  if (lines.length < 2) {
    throw new Error('Invalid CSV format')
  }
  
  // Parse header row
  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
  
  // Parse data rows
  const products: Product[] = []
  
  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVLine(lines[i])
    
    if (values.length < headers.length) continue
    
    const product = createProductFromRow(headers, values)
    if (product) {
      products.push(product)
    }
  }
  
  return products
}

function parseCSVLine(line: string): string[] {
  const result: string[] = []
  let current = ''
  let inQuotes = false
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i]
    
    if (char === '"') {
      inQuotes = !inQuotes
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim())
      current = ''
    } else {
      current += char
    }
  }
  
  result.push(current.trim())
  return result
}

function createProductFromRow(headers: string[], values: string[]): Product | null {
  try {
    const getField = (fieldName: string): string => {
      const index = headers.findIndex(h => h.toLowerCase().includes(fieldName.toLowerCase()))
      return index >= 0 ? values[index]?.replace(/"/g, '') || '' : ''
    }

    const id = getField('id') || getField('Id') || String(Math.random())
    const name = getField('name') || getField('Name') || 'Unknown Product'
    const price = parseFloat(getField('price') || getField('Price') || '0')
    const season = getField('season') || getField('Season') || 'All Season'

    // Create default images - only one image for the new layout
    const images = [
      "/images/onion-seeds-placeholder.svg"
    ]

    // Define the expected headers in order
    const expectedHeaders = [
      'Sowing Season',
      'Recommended  Area for Cultivation',
      'Seed Quantity',
      'Bulb Colour',
      'Bulb Shape',
      'Bulb Weight',
      'Maturity Period',
      'Market Price',
      'Expected Yield',
      'Characterstics of Variety',
      'बुआई का मौसम',
     "खेती के लिए सिफ़ारिश क्षेत्र",
      "बीज की मात्रा",
     "प्याज का रंग",
     "प्याज का आकार",
     "प्याज का वजन",
     "परिपक्वता अवधि",
     "बाजार कीमत",
     "अपेक्षित उपज",
     "नस्ल की विशेषताएँ"


    ]

    // Create dynamic product details with headers from sheet
    const productDetails: ProductDetail[] = []

    for (const expectedHeader of expectedHeaders) {
      const headerIndex = headers.findIndex(h =>
        h.trim().toLowerCase() === expectedHeader.toLowerCase() ||
        h.trim().toLowerCase().includes(expectedHeader.toLowerCase()) ||
        expectedHeader.toLowerCase().includes(h.trim().toLowerCase())
      )

      if (headerIndex >= 0) {
        const actualHeader = headers[headerIndex].trim()
        const value = values[headerIndex]?.replace(/"/g, '').trim() || 'Not specified'
        productDetails.push({
          header: actualHeader,
          value: value
        })
      } else {
        // Fallback with English header if not found
        productDetails.push({
          header: expectedHeader,
          value: 'Not specified'
        })
      }
    }

    // Legacy details for backward compatibility
    const legacyDetails = {
      sowingSeason: getField('Sowing Season') || getField('sowing season') || 'Rabbi Season',
      recommendedArea: getField('Recommended  Area for Cultivation') || getField('Recommended Area') || 'Maharashtra, Karnataka',
      seedQuantity: getField('Seed Quantity') || getField('seed quantity') || '500g per acre',
      bulbColour: getField('Bulb Colour') || getField('bulb colour') || getField('colour') || 'Red',
      bulbShape: getField('Bulb Shape') || getField('bulb shape') || getField('shape') || 'Round',
      bulbWeight: getField('Bulb Weight') || getField('bulb weight') || '50-80g',
      maturityPeriod: getField('Maturity Period') || getField('maturity period') || getField('maturity') || '90-120 days',
      marketPrice: getField('Market Price') || getField('market price') || '₹25-35 per kg',
      expectedYield: getField('Expected Yield') || getField('expected yield') || getField('yield') || '25-30 tons/hectare',
      characteristics: getField('Characterstics of Variety') || getField('characteristics') || getField('variety characteristics') || 'High yielding, disease resistant variety'
    }

    // Get descriptions for legacy support
    const descEnglish = getField('desc english') || getField('description') || 'Premium quality onion seeds.'
    const descMarathi = getField('desc marathi') || getField('desc first language') || 'उत्कृष्ट गुणवत्तेचे कांदा बियाणे.'
    const descHindi = getField('desc hindi') || 'उत्कृष्ट गुणवत्ता के प्याज के बीज।'

    const specifications = `Colour: ${legacyDetails.bulbColour}, Shape: ${legacyDetails.bulbShape}, Weight: ${legacyDetails.bulbWeight}, Maturity: ${legacyDetails.maturityPeriod}, Yield: ${legacyDetails.expectedYield}`

    const product: Product = {
      id,
      name,
      price,
      images,
      season,
      productDetails,
      legacyDetails,
      productInfo: {
        english: {
          description: descEnglish,
          specifications: specifications
        },
        marathi: {
          description: descMarathi,
          specifications: `रंग: ${legacyDetails.bulbColour}, आकार: ${legacyDetails.bulbShape}, वजन: ${legacyDetails.bulbWeight}, परिपक्वता: ${legacyDetails.maturityPeriod}, उत्पादन: ${legacyDetails.expectedYield}`
        },
        hindi: {
          description: descHindi,
          specifications: `रंग: ${legacyDetails.bulbColour}, आकार: ${legacyDetails.bulbShape}, वजन: ${legacyDetails.bulbWeight}, परिपक्वता: ${legacyDetails.maturityPeriod}, उत्पादन: ${legacyDetails.expectedYield}`
        }
      },
      sowingInfo: {
        english: {
          sowingTime: "October to December (Nursery), December to January (Transplanting)",
          soilType: "Well-drained loamy soil with pH 6.0-7.5, rich in organic matter",
          spacing: "Nursery: 10cm x 5cm, Field: 15cm x 10cm",
          irrigation: "Drip irrigation recommended, avoid waterlogging"
        },
        marathi: {
          sowingTime: "ऑक्टोबर ते डिसेंबर (रोपवाटिका), डिसेंबर ते जानेवारी (रोपणी)",
          soilType: "चांगली निचरा असलेली दोमट माती pH 6.0-7.5, सेंद्रिय पदार्थांनी समृद्ध",
          spacing: "रोपवाटिका: 10 सेमी x 5 सेमी, शेत: 15 सेमी x 10 सेमी",
          irrigation: "ठिबक सिंचन शिफारसीय, पाणी साचू देऊ नका"
        },
        hindi: {
          sowingTime: "अक्टूबर से दिसंबर (नर्सरी), दिसंबर से जनवरी (रोपाई)",
          soilType: "अच्छी जल निकासी वाली दोमट मिट्टी pH 6.0-7.5, जैविक पदार्थों से भरपूर",
          spacing: "नर्सरी: 10 सेमी x 5 सेमी, खेत: 15 सेमी x 10 सेमी",
          irrigation: "ड्रिप सिंचाई की सिफारिश, जलभराव से बचें"
        }
      }
    }
    
    return product
  } catch (error) {
    console.error('Error creating product from row:', error)
    return null
  }
}

function getFallbackProducts(): Product[] {
  // Return a few sample products as fallback
  return [
    {
      id: "fallback-1",
      name: "Amber Red Onion Seeds",
      price: 450,
      images: [
        "/images/onion-seeds-placeholder.svg"
      ],
      season: "Rabbi Season (Oct-Mar)",
      productDetails: [
        { header: "Sowing Season", value: "Rabbi Season (October to March)" },
        { header: "Recommended Area for Cultivation", value: "Maharashtra, Karnataka, Andhra Pradesh" },
        { header: "Seed Quantity", value: "500g per acre" },
        { header: "Bulb Colour", value: "Deep Red" },
        { header: "Bulb Shape", value: "Round to Flat Round" },
        { header: "Bulb Weight", value: "60-80g" },
        { header: "Maturity Period", value: "110-120 days" },
        { header: "Market Price", value: "₹30-40 per kg" },
        { header: "Expected Yield", value: "25-30 tons per hectare" },
        { header: "Characteristics of Variety", value: "High yielding variety with excellent storage quality, disease resistant, uniform bulb size" }
      ],
      legacyDetails: {
        sowingSeason: "Rabbi Season (October to March)",
        recommendedArea: "Maharashtra, Karnataka, Andhra Pradesh",
        seedQuantity: "500g per acre",
        bulbColour: "Deep Red",
        bulbShape: "Round to Flat Round",
        bulbWeight: "60-80g",
        maturityPeriod: "110-120 days",
        marketPrice: "₹30-40 per kg",
        expectedYield: "25-30 tons per hectare",
        characteristics: "High yielding variety with excellent storage quality, disease resistant, uniform bulb size"
      },
      productInfo: {
        english: {
          description: "Premium quality red onion seeds specially developed for Indian climate conditions",
          specifications: "Germination: 90-95%, Purity: 98%, Disease resistance: High, Yield: 25-30 tons/hectare"
        },
        marathi: {
          description: "भारतीय हवामान परिस्थितीसाठी विशेष विकसित केलेले उत्कृष्ट गुणवत्तेचे लाल कांदा बियाणे.",
          specifications: "अंकुरण: ९०-९५%, शुद्धता: ९८%, रोग प्रतिकारक क्षमता: उच्च, उत्पादन: २५-३० टन/हेक्टर"
        },
        hindi: {
          description: "भारतीय जलवायु परिस्थितियों के लिए विशेष रूप से विकसित उत्कृष्ट गुणवत्ता के लाल प्याज के बीज।",
          specifications: "अंकुरण: 90-95%, शुद्धता: 98%, रोग प्रतिरोधक क्षमता: उच्च, उत्पादन: 25-30 टन/हेक्टेयर"
        }
      },
      sowingInfo: {
        english: {
          sowingTime: "October to December (Nursery), December to January (Transplanting)",
          soilType: "Well-drained loamy soil with pH 6.0-7.5, rich in organic matter",
          spacing: "Nursery: 10cm x 5cm, Field: 15cm x 10cm",
          irrigation: "Drip irrigation recommended, avoid waterlogging"
        },
        marathi: {
          sowingTime: "ऑक्टोबर ते डिसेंबर (रोपवाटिका), डिसेंबर ते जानेवारी (रोपणी)",
          soilType: "चांगली निचरा असलेली दोमट माती pH 6.0-7.5, सेंद्रिय पदार्थांनी समृद्ध",
          spacing: "रोपवाटिका: 10 सेमी x 5 सेमी, शेत: 15 सेमी x 10 सेमी",
          irrigation: "ठिबक सिंचन शिफारसीय, पाणी साचू देऊ नका"
        },
        hindi: {
          sowingTime: "अक्टूबर से दिसंबर (नर्सरी), दिसंबर से जनवरी (रोपाई)",
          soilType: "अच्छी जल निकासी वाली दोमट मिट्टी pH 6.0-7.5, जैविक पदार्थों से भरपूर",
          spacing: "नर्सरी: 10 सेमी x 5 सेमी, खेत: 15 सेमी x 10 सेमी",
          irrigation: "ड्रिप सिंचाई की सिफारिश, जलभराव से बचें"
        }
      }
    }
  ]
}
