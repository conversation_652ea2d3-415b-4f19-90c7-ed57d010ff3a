'use client'

export default function IndiaMap() {
  const presentStates = [
    'Maharashtra', 'Karnataka', 'Madhya Pradesh', 'Gujarat',
    'Chhattisgarh', 'Jharkhand', 'Uttar Pradesh', 'Bihar', 'Rajasthan'
  ]

  return (
    <section className="py-12 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Our Presence Across India
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Serving farmers across 9 states with premium quality onion seeds and agricultural expertise
          </p>
        </div>

        <div className="flex flex-col lg:flex-row items-center justify-center gap-12">
          {/* India Map Image */}
          <div className="relative w-full max-w-2xl">
            <img
              src="/images/India_Map.png"
              alt="India Map showing our presence across states"
              className="w-full h-auto object-contain rounded-lg shadow-lg"
              style={{ maxHeight: '600px' }}
            />
          </div>

          {/* States List */}
          <div className="lg:w-1/3">
            <h3 className="text-xl font-semibold text-gray-800 mb-6">States We Serve</h3>
            <div className="grid grid-cols-1 gap-3">
              {presentStates.map((state, index) => (
                <div
                  key={index}
                  className="flex items-center p-3 bg-purple-50 rounded-lg border border-purple-200 hover:bg-purple-100 transition-colors duration-200"
                >
                  <div className="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                  <span className="text-gray-800 font-medium">{state}</span>
                </div>
              ))}
            </div>
            
            <div className="mt-8 p-4 bg-gradient-to-r from-purple-600 to-violet-600 rounded-lg text-white">
              <h4 className="font-semibold mb-2">Growing Network</h4>
              <p className="text-sm text-purple-100">
                Expanding our reach to serve more farmers across India with quality seeds and agricultural support.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
