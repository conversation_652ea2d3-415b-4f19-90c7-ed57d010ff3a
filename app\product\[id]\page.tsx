'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Loader2 } from 'lucide-react'
import UserDetailsModal from '@/components/user-details-modal'

// COMPREHENSIVE Google Drive Image Handler - Handles ALL scenarios
function ProductImage({
  imageUrl,
  alt,
  productId
}: {
  imageUrl: string;
  alt: string;
  productId?: string;
}) {
  const [currentImageUrl, setCurrentImageUrl] = useState("/images/onion-seeds-placeholder.svg");
  const [attemptIndex, setAttemptIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  // MULTIPLE Google Drive URL conversion strategies
  const getAllPossibleUrls = (url: string): string[] => {
    if (!url || url.trim() === '') return [];

    const urls: string[] = [];
    const cleanUrl = url.trim();

    // Extract file ID using multiple patterns
    const fileIdPatterns = [
      /\/file\/d\/([a-zA-Z0-9-_]+)/,                    // /file/d/FILE_ID
      /[?&]id=([a-zA-Z0-9-_]+)/,                        // ?id=FILE_ID or &id=FILE_ID
      /drive\.google\.com\/.*\/([a-zA-Z0-9-_]{25,})/,   // Any drive URL with long ID
      /([a-zA-Z0-9-_]{25,})/                            // Just the ID itself if pasted
    ];

    let fileId = '';
    for (const pattern of fileIdPatterns) {
      const match = cleanUrl.match(pattern);
      if (match && match[1] && match[1].length >= 25) {
        fileId = match[1];
        break;
      }
    }

    if (fileId) {
      // Strategy 1: Standard uc export view
      urls.push(`https://drive.google.com/uc?export=view&id=${fileId}`);

      // Strategy 2: uc without export parameter
      urls.push(`https://drive.google.com/uc?id=${fileId}`);

      // Strategy 3: Thumbnail with large size
      urls.push(`https://drive.google.com/thumbnail?id=${fileId}&sz=w1000-h1000`);

      // Strategy 4: GoogleUserContent CDN
      urls.push(`https://lh3.googleusercontent.com/d/${fileId}=w1000`);

      // Strategy 5: GoogleUserContent alternative
      urls.push(`https://lh3.googleusercontent.com/d/${fileId}`);

      // Strategy 6: Drive proxy (sometimes works)
      urls.push(`https://drive.google.com/uc?export=download&id=${fileId}`);

      // Strategy 7: Alternative thumbnail sizes
      urls.push(`https://drive.google.com/thumbnail?id=${fileId}&sz=w800-h800`);
      urls.push(`https://drive.google.com/thumbnail?id=${fileId}&sz=w500-h500`);
    }

    // Strategy 8: If it's already a direct URL, try it as-is
    if (cleanUrl.startsWith('http') && !cleanUrl.includes('/file/d/')) {
      urls.unshift(cleanUrl); // Add to beginning
    }

    return urls;
  };

  // Attempt to load image with multiple fallback strategies
  useEffect(() => {
    const loadImage = async () => {
      setIsLoading(true);
      const debug: string[] = [];
      debug.push(`🔍 Original URL: ${imageUrl}`);

      if (!imageUrl || imageUrl.trim() === '') {
        debug.push('❌ No URL provided');
        setCurrentImageUrl("/images/onion-seeds-placeholder.svg");
        setDebugInfo(debug);
        setIsLoading(false);
        return;
      }

      const possibleUrls = getAllPossibleUrls(imageUrl);
      debug.push(`🎯 Generated ${possibleUrls.length} possible URLs`);

      // Try each URL until one works
      for (let i = 0; i < possibleUrls.length; i++) {
        const testUrl = possibleUrls[i];
        debug.push(`🔄 Trying URL ${i + 1}: ${testUrl}`);

        try {
          const success = await testImageUrl(testUrl);
          if (success) {
            debug.push(`✅ SUCCESS with URL ${i + 1}`);
            setCurrentImageUrl(testUrl);
            setAttemptIndex(i);
            setDebugInfo(debug);
            setIsLoading(false);
            return;
          } else {
            debug.push(`❌ Failed URL ${i + 1}`);
          }
        } catch (error) {
          debug.push(`💥 Error with URL ${i + 1}: ${error}`);
        }
      }

      // All URLs failed, try local fallbacks
      debug.push('🔄 All Google Drive URLs failed, trying local fallbacks');

      if (productId) {
        const localUrl = `/images/products/product-${productId}.jpg`;
        debug.push(`🔄 Trying local product image: ${localUrl}`);
        const localSuccess = await testImageUrl(localUrl);
        if (localSuccess) {
          debug.push('✅ SUCCESS with local product image');
          setCurrentImageUrl(localUrl);
          setDebugInfo(debug);
          setIsLoading(false);
          return;
        }
      }

      // Final fallback
      debug.push('🔄 Using final placeholder fallback');
      setCurrentImageUrl("/images/onion-seeds-placeholder.svg");
      setDebugInfo(debug);
      setIsLoading(false);
    };

    loadImage();
  }, [imageUrl, productId]);

  // Test if an image URL actually works
  const testImageUrl = (url: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;

      // Timeout after 5 seconds
      setTimeout(() => resolve(false), 5000);
    });
  };

  // Handle image load errors (backup for the backup)
  const handleImageError = async () => {
    console.log('🚨 Image error handler triggered');
    console.log('Debug info:', debugInfo);

    // Try next URL in the list if available
    const possibleUrls = getAllPossibleUrls(imageUrl);
    const nextIndex = attemptIndex + 1;

    if (nextIndex < possibleUrls.length) {
      console.log(`🔄 Trying next URL (${nextIndex + 1}/${possibleUrls.length})`);
      setAttemptIndex(nextIndex);
      setCurrentImageUrl(possibleUrls[nextIndex]);
    } else if (productId) {
      console.log('🔄 Falling back to local product image');
      setCurrentImageUrl(`/images/products/product-${productId}.jpg`);
    } else {
      console.log('🔄 Using final placeholder');
      setCurrentImageUrl("/images/onion-seeds-placeholder.svg");
    }
  };

  // Debug component (remove in production)
  const DebugPanel = () => {
    if (process.env.NODE_ENV !== 'development') return null;

    return (
      <div className="absolute top-0 left-0 bg-black bg-opacity-75 text-white text-xs p-2 max-w-xs overflow-hidden z-10">
        <div className="font-bold">Image Debug:</div>
        {debugInfo.map((info, i) => (
          <div key={i} className="truncate">{info}</div>
        ))}
        <div className="mt-1 font-bold">Current: {currentImageUrl}</div>
      </div>
    );
  };

  return (
    <div className="relative w-full h-full">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-purple-50">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      )}
      <img
        src={currentImageUrl}
        alt={alt}
        className="w-full h-full object-cover"
        onError={handleImageError}
        onLoad={() => setIsLoading(false)}
      />
      <DebugPanel />
    </div>
  );
}

interface ProductData {
  id: string
  name: string
  headers: string[]
  values: string[]
}

const SHEET_CONFIG = {
  english: { gid: "0", name: "English" },
  hindi: { gid: "1199415046", name: "हिंदी" },
  marathi: { gid: "907114351", name: "मराठी" }
}

const SHEET_ID = "1l9-NuJYb9OITmVP_lhla39K3e2ODQMuItpJ8pNYbpB0"

// Function to determine season from Column D and load appropriate JSON
async function loadStaticInfoBySeason(columnD: string) {
  const firstFiveLetters = columnD.substring(0, 5).toLowerCase()

  let jsonFile = 'staticInfo.json' // Default to Rabbi season
  if (firstFiveLetters === 'kharf' || firstFiveLetters === 'khari') {
    jsonFile = 'staticInfoKharif.json'
  }

  try {
    const response = await fetch(`/data/${jsonFile}`)
    if (!response.ok) {
      throw new Error(`Failed to load ${jsonFile}`)
    }
    return await response.json()
  } catch (error) {
    console.error(`Error loading ${jsonFile}:`, error)
    // Fallback to default staticInfo.json
    const fallbackResponse = await fetch('/data/staticInfo.json')
    return await fallbackResponse.json()
  }
}

// Function to fetch data from specific sheet
async function fetchSheetData(gid: string): Promise<ProductData[]> {
  try {
    const url = `https://docs.google.com/spreadsheets/d/${SHEET_ID}/export?format=csv&gid=${gid}`
    const response = await fetch(url, { cache: 'no-store' })
    console.log(response)
    if (!response.ok) {
      throw new Error(`Failed to fetch sheet data: ${response.status}`)
    }
    
    const csvText = await response.text()
    return parseCSVData(csvText)
  } catch (error) {
    console.error('Error fetching sheet data:', error)
    return []
  }
}

// Function to parse CSV data
function parseCSVData(csvText: string): ProductData[] {
  const lines = csvText.split('\n').filter(line => line.trim())
  if (lines.length < 2) return []
  
  // Parse headers
  const headers = parseCSVLine(lines[0])
  const products: ProductData[] = []
  
  // Parse each product row
  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVLine(lines[i])
    if (values.length >= headers.length) {
       const id = values[1]?.trim() || `product-${i}`;
        const name = values[2]?.trim() || 'Unknown Product';
      
      products.push({
        id,
        name,
        headers,
        values
      })
    }
  }
  
  return products
}

// Function to parse CSV line handling quotes
function parseCSVLine(line: string): string[] {
  const result: string[] = []
  let current = ''
  let inQuotes = false
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i]
    
    if (char === '"') {
      inQuotes = !inQuotes
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim().replace(/^"|"$/g, ''))
      current = ''
    } else {
      current += char
    }
  }
  
  result.push(current.trim().replace(/^"|"$/g, ''))
  return result
}

// Component to display product details
function ProductDetailsDisplay({ productData }: { productData: ProductData }) {
  if (!productData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No product data available</p>
      </div>
    )
  }

  // Skip first two columns (ID and Name) and display the rest
  const detailsToShow = productData.headers.slice(2).map((header, index) => ({
    header: header.trim(),
    value: productData.values[index + 2]?.trim() || 'Not specified'
  }))

  return (
    <div className="space-y-4">
      {detailsToShow.map((detail, index) => (
        index > 0 && 
          <div key={index} className="bg-gradient-to-r from-purple-50 to-violet-50 p-6 rounded-lg border border-purple-100">
          <h3 className="font-semibold text-gray-800 mb-3 text-lg">{detail.header}</h3>
          <p className="text-gray-700 leading-relaxed text-base">{detail.value}</p>
        </div>
      ))}
    </div>
  )
}



// Component to display static cultivation information
function CultivationInfoDisplay({ language, staticInfoData }: {
  language: 'english' | 'hindi' | 'marathi',
  staticInfoData: any[] | null
}) {
  const languageData = staticInfoData?.find(info => info.language === language)

  if (!languageData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No cultivation information available</p>
      </div>
    )
  }

  return (
    <div className="mt-8">
      <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-3 rounded-lg border border-green-100 mb-3">
        <h2 className="text-2xl font-bold text-purple-800 mb-2">{languageData.title}</h2>
        {/* <p className="text-green-700 text-lg">{languageData.subtitle}</p> */}
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-gray-300 bg-white rounded-lg shadow-sm">
          <thead>
            <tr className="bg-gradient-to-r from-purple-100 to-purple-300">
              <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-purple-600 w-1/4">
                Step
              </th>
              <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-purple-600">
                Details
              </th>
            </tr>
          </thead>
          <tbody>
            {languageData.sections.map((section :any, index :any) => (
              <tr key={index} className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}>
                <td className="border border-gray-300 px-4 py-4 font-medium text-gray-800 align-top">
                  {section.title}
                </td>
                <td className="border border-gray-300 px-4 py-4 text-gray-700 leading-relaxed">
                  {section.content}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default function ProductPage() {
  const params = useParams()
  const [productData, setProductData] = useState<ProductData | null>(null)
  const [activeTab, setActiveTab] = useState<'english' | 'hindi' | 'marathi'>('english')
  const [loading, setLoading] = useState(true)
  const [tabLoading, setTabLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [staticInfoData, setStaticInfoData] = useState<any[] | null>(null)
  const [showUserModal, setShowUserModal] = useState(false)
  const [modalShown, setModalShown] = useState(false)

  // Load initial English data
  useEffect(() => {
    async function loadProduct() {
      try {
        setLoading(true)
        const data = await fetchSheetData(SHEET_CONFIG.english.gid)

        const targetId = params.id as string
        const rowIndex = parseInt(targetId) - 1

        console.log(`=== INITIAL LOAD ===`)
        console.log(`URL ID: ${targetId}, Row Index: ${rowIndex}`)
        console.log(`Total products in English sheet: ${data.length}`)

        if (rowIndex >= 0 && rowIndex < data.length) {
          const product = data[rowIndex]
          console.log(`✅ Loading row ${targetId}:`, product.values[2])

          // Debug Column N (image data) - CRITICAL DEBUG INFO
          const columnNValue = product.values[13] || ""
          console.log(`🖼️ COLUMN N (Image URL):`, columnNValue)
          console.log(`🔍 Column N type:`, typeof columnNValue)
          console.log(`📏 Column N length:`, columnNValue.length)
          console.log(`🧹 Column N trimmed:`, columnNValue.trim())
          console.log(`📊 Total columns in row:`, product.values.length)
          console.log(`📋 All values:`, product.values)

          setProductData(product)

          // Load appropriate static info based on season (Column D = values[3])
          const columnD = product.values[3] || ""
          console.log(`Season from Column D: "${columnD}"`)
          const staticInfo = await loadStaticInfoBySeason(columnD)
          setStaticInfoData(staticInfo)
        } else {
          setError(`Row ${targetId} not found (only ${data.length} products available)`)
        }
      } catch (err) {
        setError('Failed to load product')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      loadProduct()
    }
  }, [params.id])

  // Show user details modal only on initial page load (not on tab changes)
  useEffect(() => {
    if (productData && !loading && !modalShown) {
      const timer = setTimeout(() => {
        setShowUserModal(true)
        setModalShown(true) // Mark modal as shown to prevent showing again
      }, 2000) // Show modal 2 seconds after page loads
      return () => clearTimeout(timer)
    }
  }, [productData, loading, modalShown])

  // Handle language tab changes
  const handleTabChange = async (language: 'english' | 'hindi' | 'marathi') => {
    setActiveTab(language)
    setTabLoading(true)

    try {
      const data = await fetchSheetData(SHEET_CONFIG[language].gid)
      const targetId = params.id as string

      console.log(`=== ${language.toUpperCase()} SHEET ===`)
      console.log(`Looking for ID: ${targetId}`)
      console.log(`Available products:`, data.slice(0, 5).map(p => ({ id: p.id, name: p.values[2] })))

      // Always use row position to ensure same product across all sheets
      const rowIndex = parseInt(targetId) - 1

      if (rowIndex >= 0 && rowIndex < data.length) {
        const product = data[rowIndex]
        console.log(`✅ Using row ${parseInt(targetId)} (index ${rowIndex}):`, product.values[2])
        setProductData(product)
      } else {
        console.log(`❌ Row ${targetId} not found in ${language} sheet`)
      }
    } catch (err) {
      console.error(`Failed to load ${language} data:`, err)
    } finally {
      setTabLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-purple-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <h1 className="text-xl font-semibold text-gray-800">Loading product details...</h1>
        </div>
      </div>
    )
  }

  if (error || !productData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-purple-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            {error || "Product Not Found"}
          </h1>
          {/* <Button onClick={() => router.push("/products")} className="bg-purple-600 hover:bg-purple-700">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Products
          </Button> */}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header with Back Button - No Navigation */}
      <div className="bg-white border-b border-purple-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* <Button
              variant="ghost"
              onClick={() => router.push("/products")}
              className="text-purple-600 hover:text-purple-700 hover:bg-purple-50"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Button> */}

            <div className="flex items-center space-x-2">
              <span className="sm:text-lg text-sm font-bold text-purple-800">Amber Seeds & Farms</span>
            </div>
          </div>
        </div>
      </div>

      {/* Product Details */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Product Image */}
          <div className="lg:col-span-1">
            <div className="aspect-square bg-purple-50 rounded-xl overflow-hidden shadow-md max-w-sm mx-auto">
              <ProductImage
                imageUrl={productData.values[13] || ""}
                alt={productData.name}
                productId={params.id as string}
              />
            </div>
          </div>

          {/* Product Information with Tabs */}
          <div className="lg:col-span-3 space-y-6">
            {/* Product Header */}
            <div className="border-b border-gray-200 pb-6">
              <h1 className="text-3xl md:text-4xl font-bold text-gray-800">{productData.name}</h1>
            
            </div>

            {/* Debug Buttons */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mb-4 space-x-2">
                <button
                  onClick={() => {
                    console.log('=== IMAGE DEBUG TEST ===')
                    const testUrl = "https://drive.google.com/file/d/18o3QBipHC5kcuTm5UIVa4ly2NlmiSTtE/view?usp=sharing"
                    console.log('Test URL:', testUrl)

                    // Test direct image loading
                    const testImg = new Image()
                    testImg.onload = () => console.log('✅ Direct image test SUCCESS')
                    testImg.onerror = () => console.log('❌ Direct image test FAILED')
                    testImg.src = "https://drive.google.com/uc?export=view&id=18o3QBipHC5kcuTm5UIVa4ly2NlmiSTtE"

                    console.log('Image test started - check console for results')
                  }}
                  className="bg-blue-500 text-white px-4 py-2 rounded text-sm"
                >
                  Test Image URL
                </button>
                <button
                  onClick={() => {
                    console.log('=== CURRENT PRODUCT DATA ===')
                    console.log('Product Data:', productData)
                    console.log('Column N Value:', productData?.values[13])
                    console.log('All Values:', productData?.values)
                  }}
                  className="bg-green-500 text-white px-4 py-2 rounded text-sm"
                >
                  Debug Product Data
                </button>
                <button
                  onClick={async () => {
                    const testUrls = [
                      "https://drive.google.com/uc?export=view&id=18o3QBipHC5kcuTm5UIVa4ly2NlmiSTtE",
                      "https://drive.google.com/uc?id=18o3QBipHC5kcuTm5UIVa4ly2NlmiSTtE",
                      "https://drive.google.com/thumbnail?id=18o3QBipHC5kcuTm5UIVa4ly2NlmiSTtE&sz=w1000-h1000",
                      "https://lh3.googleusercontent.com/d/18o3QBipHC5kcuTm5UIVa4ly2NlmiSTtE=w1000"
                    ]

                    console.log('=== TESTING ALL URL FORMATS ===')
                    for (let i = 0; i < testUrls.length; i++) {
                      const url = testUrls[i]
                      console.log(`Testing URL ${i + 1}:`, url)

                      try {
                        const response = await fetch(url, { method: 'HEAD' })
                        console.log(`URL ${i + 1} Response:`, response.status, response.statusText)
                      } catch (error) {
                        console.log(`URL ${i + 1} Error:`, error)
                      }
                    }
                  }}
                  className="bg-purple-500 text-white px-4 py-2 rounded text-sm"
                >
                  Test All URLs
                </button>
              </div>
            )}

            {/* Language Tabs */}
            <Tabs value={activeTab} onValueChange={(value) => handleTabChange(value as 'english' | 'hindi' | 'marathi')} className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-purple-50 mb-8">
                <TabsTrigger value="english" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
                  English
                </TabsTrigger>
                <TabsTrigger value="hindi" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
                  हिंदी
                </TabsTrigger>
                <TabsTrigger value="marathi" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
                  मराठी
                </TabsTrigger>
              </TabsList>

              <TabsContent value="english" className="mt-6">
                {tabLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
                      <p className="text-gray-600">Please wait...</p>
                    </div>
                  </div>
                ) : (
                  <>
                    <ProductDetailsDisplay productData={productData} />
                    <CultivationInfoDisplay language="english" staticInfoData={staticInfoData} />
                  </>
                )}
              </TabsContent>

              <TabsContent value="hindi" className="mt-6">
                {tabLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
                      <p className="text-gray-600">कृपया प्रतीक्षा करें...</p>
                    </div>
                  </div>
                ) : (
                  <>
                    <ProductDetailsDisplay productData={productData} />
                    <CultivationInfoDisplay language="hindi" staticInfoData={staticInfoData} />
                  </>
                )}
              </TabsContent>

              <TabsContent value="marathi" className="mt-6">
                {tabLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
                      <p className="text-gray-600">कृपया प्रतीक्षा करा...</p>
                    </div>
                  </div>
                ) : (
                  <>
                    <ProductDetailsDisplay productData={productData} />
                    <CultivationInfoDisplay language="marathi" staticInfoData={staticInfoData} />
                  </>
                )}
              </TabsContent>
            </Tabs>

            {/* Action Buttons */}
            {/* <div className="mt-8 space-y-4">
              <Button size="lg" className="w-full bg-purple-600 hover:bg-purple-700 text-white py-4">
                Contact for Pricing & Availability
              </Button>
              <Button size="lg" variant="outline" className="w-full border-purple-300 text-purple-600 hover:bg-purple-50 py-4">
                Download Product Brochure
              </Button>
            </div> */}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-purple-900 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-purple-300">
            © 2025 Amber Seeds & Farms. All rights reserved.
          </p>
        </div>
      </div>

      {/* User Details Modal */}
      <UserDetailsModal
        isOpen={showUserModal}
        onClose={() => setShowUserModal(false)}
      />
    </div>
  )
}
