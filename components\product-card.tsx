"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/language-context"

// Simple product interface for compatibility
interface SimpleProduct {
  id: string
  name: string
  // price: number
  season: string
  images: string[]
}

interface ProductCardProps {
  product: SimpleProduct
}

export default function ProductCard({ product }: ProductCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const { t } = useLanguage()

  return (
    <Link href={`/product/${product.id}`}>
      <Card
        className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 overflow-hidden border-purple-100 grow-on-hover"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative overflow-hidden">
          <Image
            src={product.images[0] || "/placeholder.svg"}
            alt={product.name}
            width={300}
            height={250}
            className={`w-full h-48 object-cover transition-transform duration-500 ${
              isHovered ? "scale-110" : "scale-100"
            }`}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">{product.season}</Badge>
          </div>

          <h3 className="font-semibold text-lg text-gray-800 mb-2 group-hover:text-purple-600 transition-colors">
            {product.name}
          </h3>
          {/* <div className="flex items-center justify-between">
            <span className="text-2xl font-bold text-purple-600">₹{product.price}</span>
            <span className="text-sm text-gray-500">{t("product.perkg")}</span>
          </div> */}

          <div className="mt-3 w-full h-1 bg-gray-200 rounded-full overflow-hidden">
            <div
              className={`h-full bg-gradient-to-r from-purple-400 to-purple-600 transition-all duration-500 ${
                isHovered ? "w-full" : "w-0"
              }`}
            />
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
