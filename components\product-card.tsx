"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/language-context"

// Simplified component that uses a working Google Drive format
function ProductCardImage({ src, alt, isHovered }: { src: string, alt: string, isHovered: boolean }) {
  const [imageSrc, setImageSrc] = useState<string>("")
  const [hasError, setHasError] = useState(false)

  console.log(`🎯 ProductCard received URL:`, src)

  // Extract file ID and convert to working format
  const getWorkingImageUrl = (url: string) => {
    if (!url || url.includes('placeholder')) {
      return "/images/onion-seeds-placeholder.svg"
    }

    // Handle Google Drive URLs by converting to direct format
    const match = url.match(/\/file\/d\/([a-zA-Z0-9-_]+)/)
    if (match) {
      const fileId = match[1]

      // Use Google Drive direct format (may still have CORS issues)
      const workingUrl = `https://lh3.googleusercontent.com/d/${fileId}`
      console.log(`🔄 Converted Drive URL to:`, workingUrl)
      return workingUrl
    }

    // If it's already a direct URL, use it
    if (url.startsWith('http')) {
      console.log(`🌐 Using direct URL:`, url)
      return url
    }

    // Fallback to placeholder
    console.log(`❌ Could not process URL, using placeholder`)
    return "/images/onion-seeds-placeholder.svg"
  }

  // Set the image source when component mounts or src changes
  useEffect(() => {
    const workingUrl = getWorkingImageUrl(src)
    setImageSrc(workingUrl)
    setHasError(false)
  }, [src])

  const handleError = () => {
    console.log(`🚫 Product card image failed to load:`, imageSrc)
    setHasError(true)
    setImageSrc("/images/onion-seeds-placeholder.svg")
  }

  const handleLoad = () => {
    console.log(`✅ Product card image loaded successfully:`, imageSrc)
  }

  return (
    <img
      src={hasError ? "/images/onion-seeds-placeholder.svg" : imageSrc}
      alt={alt}
      className={`w-full h-48 object-cover transition-transform duration-500 ${
        isHovered ? "scale-110" : "scale-100"
      }`}
      onError={handleError}
      onLoad={handleLoad}
    />
  )
}

// Simple product interface for compatibility
interface SimpleProduct {
  id: string
  name: string
  // price: number
  season: string
  images: string[]
}

interface ProductCardProps {
  product: SimpleProduct
}

export default function ProductCard({ product }: ProductCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const { t } = useLanguage()

  return (
    <Link href={`/product/${product.id}`}>
      <Card
        className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 overflow-hidden border-purple-100 grow-on-hover"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative overflow-hidden">
          <ProductCardImage
            src={product.images[0] || "/images/onion-seeds-placeholder.svg"}
            alt={product.name}
            isHovered={isHovered}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">{product.season}</Badge>
          </div>

          <h3 className="font-semibold text-lg text-gray-800 mb-2 group-hover:text-purple-600 transition-colors">
            {product.name}
          </h3>
          {/* <div className="flex items-center justify-between">
            <span className="text-2xl font-bold text-purple-600">₹{product.price}</span>
            <span className="text-sm text-gray-500">{t("product.perkg")}</span>
          </div> */}

          <div className="mt-3 w-full h-1 bg-gray-200 rounded-full overflow-hidden">
            <div
              className={`h-full bg-gradient-to-r from-purple-400 to-purple-600 transition-all duration-500 ${
                isHovered ? "w-full" : "w-0"
              }`}
            />
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
