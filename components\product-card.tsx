"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/language-context"

// Google Drive Image Handler for Product Cards
function ProductCardImage({
  imageUrl,
  alt,
  productId
}: {
  imageUrl: string;
  alt: string;
  productId?: string;
}) {
  const [currentImageUrl, setCurrentImageUrl] = useState("/images/onion-seeds-placeholder.svg");
  const [attemptIndex, setAttemptIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // MULTIPLE Google Drive URL conversion strategies
  const getAllPossibleUrls = (url: string): string[] => {
    if (!url || url.trim() === '') return [];

    const urls: string[] = [];
    const cleanUrl = url.trim();

    // Extract file ID using multiple patterns
    const fileIdPatterns = [
      /\/file\/d\/([a-zA-Z0-9-_]+)/,                    // /file/d/FILE_ID
      /[?&]id=([a-zA-Z0-9-_]+)/,                        // ?id=FILE_ID or &id=FILE_ID
      /drive\.google\.com\/.*\/([a-zA-Z0-9-_]{25,})/,   // Any drive URL with long ID
      /([a-zA-Z0-9-_]{25,})/                            // Just the ID itself if pasted
    ];

    let fileId = '';
    for (const pattern of fileIdPatterns) {
      const match = cleanUrl.match(pattern);
      if (match && match[1] && match[1].length >= 25) {
        fileId = match[1];
        break;
      }
    }

    if (fileId) {
      // Strategy 1: Standard uc export view
      urls.push(`https://drive.google.com/uc?export=view&id=${fileId}`);

      // Strategy 2: uc without export parameter
      urls.push(`https://drive.google.com/uc?id=${fileId}`);

      // Strategy 3: Thumbnail with large size
      urls.push(`https://drive.google.com/thumbnail?id=${fileId}&sz=w1000-h1000`);

      // Strategy 4: GoogleUserContent CDN
      urls.push(`https://lh3.googleusercontent.com/d/${fileId}=w1000`);

      // Strategy 5: GoogleUserContent alternative
      urls.push(`https://lh3.googleusercontent.com/d/${fileId}`);
    }

    // Strategy 6: If it's already a direct URL, try it as-is
    if (cleanUrl.startsWith('http') && !cleanUrl.includes('/file/d/')) {
      urls.unshift(cleanUrl); // Add to beginning
    }

    return urls;
  };

  // Attempt to load image with multiple fallback strategies
  useEffect(() => {
    const loadImage = async () => {
      setIsLoading(true);

      if (!imageUrl || imageUrl.trim() === '') {
        setCurrentImageUrl("/images/onion-seeds-placeholder.svg");
        setIsLoading(false);
        return;
      }

      const possibleUrls = getAllPossibleUrls(imageUrl);

      // Try each URL until one works
      for (let i = 0; i < possibleUrls.length; i++) {
        const testUrl = possibleUrls[i];

        try {
          const success = await testImageUrl(testUrl);
          if (success) {
            setCurrentImageUrl(testUrl);
            setAttemptIndex(i);
            setIsLoading(false);
            return;
          }
        } catch (error) {
          console.error(`Error with URL ${i + 1}:`, error);
        }
      }

      // All URLs failed, try local fallbacks
      if (productId) {
        const localUrl = `/images/products/product-${productId}.jpg`;
        const localSuccess = await testImageUrl(localUrl);
        if (localSuccess) {
          setCurrentImageUrl(localUrl);
          setIsLoading(false);
          return;
        }
      }

      // Final fallback
      setCurrentImageUrl("/images/onion-seeds-placeholder.svg");
      setIsLoading(false);
    };

    loadImage();
  }, [imageUrl, productId]);

  // Test if an image URL actually works
  const testImageUrl = (url: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = document.createElement('img');
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;

      // Timeout after 3 seconds for product cards (faster than detail page)
      setTimeout(() => resolve(false), 3000);
    });
  };

  // Handle image load errors (backup for the backup)
  const handleImageError = async () => {
    // Try next URL in the list if available
    const possibleUrls = getAllPossibleUrls(imageUrl);
    const nextIndex = attemptIndex + 1;

    if (nextIndex < possibleUrls.length) {
      setAttemptIndex(nextIndex);
      setCurrentImageUrl(possibleUrls[nextIndex]);
    } else if (productId) {
      setCurrentImageUrl(`/images/products/product-${productId}.jpg`);
    } else {
      setCurrentImageUrl("/images/onion-seeds-placeholder.svg");
    }
  };

  return (
    <div className="relative w-full h-full">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-purple-50">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
        </div>
      )}
      <img
        src={currentImageUrl}
        alt={alt}
        className="w-full h-48 object-cover transition-transform duration-500"
        onError={handleImageError}
        onLoad={() => setIsLoading(false)}
      />
    </div>
  );
}

// Simple product interface for compatibility
interface SimpleProduct {
  id: string
  name: string
  // price: number
  season: string
  images: string[]
  imageUrl?: string // Google Drive image URL from column 14
}

interface ProductCardProps {
  product: SimpleProduct
}

export default function ProductCard({ product }: ProductCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const { t } = useLanguage()

  return (
    <Link href={`/product/${product.id}`}>
      <Card
        className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 overflow-hidden border-purple-100 grow-on-hover"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative overflow-hidden">
          <div className={`transition-transform duration-500 ${
            isHovered ? "scale-110" : "scale-100"
          }`}>
            <ProductCardImage
              imageUrl={product.imageUrl || ""}
              alt={product.name}
              productId={product.id}
            />
          </div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">{product.season}</Badge>
          </div>

          <h3 className="font-semibold text-lg text-gray-800 mb-2 group-hover:text-purple-600 transition-colors">
            {product.name}
          </h3>
          {/* <div className="flex items-center justify-between">
            <span className="text-2xl font-bold text-purple-600">₹{product.price}</span>
            <span className="text-sm text-gray-500">{t("product.perkg")}</span>
          </div> */}

          <div className="mt-3 w-full h-1 bg-gray-200 rounded-full overflow-hidden">
            <div
              className={`h-full bg-gradient-to-r from-purple-400 to-purple-600 transition-all duration-500 ${
                isHovered ? "w-full" : "w-0"
              }`}
            />
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
