import { useLanguage } from "@/components/language-context"
import { translations, Language } from "@/lib/translations"

export function useTranslation() {
  const { language } = useLanguage()
  
  const t = (key: string): string => {
    const keys = key.split('.')
    let value: any = translations[language as Language]
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        // Fallback to English if key not found
        value = translations.english
        for (const fallbackKey of keys) {
          if (value && typeof value === 'object' && fallbackKey in value) {
            value = value[fallbackKey]
          } else {
            return key // Return key if not found in fallback
          }
        }
        break
      }
    }
    
    return typeof value === 'string' ? value : key
  }
  
  return { t, language }
}
