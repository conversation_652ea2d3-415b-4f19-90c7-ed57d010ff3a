import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { LanguageProvider } from "@/components/language-context"
import { Toaster } from "@/components/ui/sonner"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Amber Seeds & Farms",
  description:
    "Leading producer of premium quality onions and onion products. Fresh, organic, and processed onion varieties for all your needs.",
  keywords: "onions, agriculture, farming, organic onions, onion products, fresh produce, Divya Seeds",
    generator: 'Auspiverse Innovations'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <LanguageProvider>{children}</LanguageProvider>
        <Toaster />
      </body>
    </html>
  )
}
