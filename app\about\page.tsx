"use client"

import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Leaf, Users, Award, Target, Calendar, MapPin, TrendingUp, Heart, CheckCircle, Globe, Shield } from "lucide-react"
import { useTranslation } from "@/hooks/useTranslation"
import Link from "next/link"

export default function AboutPage() {
  const { t } = useTranslation()

  const timelineEvents = [
    {
      year: "1994",
      title: "Foundation and started sales in Maharashtra",
      description: "Amber Seeds & Farms was established with a vision to revolutionize onion seeds farming in India.",
      icon: Leaf,
      color: "bg-purple-500"
    },
    {
      year: "1995",
      title: "Developed our first 3 varieties",
      description: "Developed our first 3 high-yield onion varieties adapted for Maharashtra climate.",
      icon: Award,
      color: "bg-violet-500"
    },
    {
      year: "1995",
      title: "Started Sales in districts like Nashik and Ahilyadevinagar",
      description: "Expanded operations to serve over 1000 farmers across Different districts in Maharashtra.",
      icon: Users,
      color: "bg-indigo-500"
    },
    {
      year: "1997",
      title: "Started sales in other districts in Maharashtra",
      description: "Expanded operations across Maharashtra.",
      icon: Users,
      color: "bg-indigo-500"
    },
    {
      year: "1999",
      title: "Expanded to Madhya Pradesh and Karnatak, Gujarat, and Rajasthan ",
      description: "Expanded operations across Different States.",
      icon: Target,
      color: "bg-purple-600"
    },
    {
      year: "2003",
      title: "Expanded to Uttar Pradesh, Bihar, Chhattisgarh, Jharkhand",
      description: "Expanded operations across India.",
      icon: TrendingUp,
      color: "bg-violet-600"
    },
    {
      year: "2017",
      title: "Developed India’s best onion variety – Pratham F",
      description: "– Developed India’s best onion variety – Pratham F and exported it across india",
      icon: Heart,
      color: "bg-purple-700"
    }
  ]

  const values = [
    {
      icon: Leaf,
      title: "Sustainability",
      description:
        "Committed to environmentally friendly farming practices that preserve our land for future generations",
    },
    {
      icon: Award,
      title: "Quality Excellence",
      description: "Maintaining the highest standards in every aspect of our production and processing.",
    },
    {
      icon: Users,
      title: "Community Focus",
      description: "Supporting local farmers and communities through fair trade and employment opportunities.",
    },
    {
      icon: Globe,
      title: "Global Reach",
      description: "Serving customers worldwide while maintaining our roots in traditional farming values.",
    },
  ]

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="relative py-24 purple-pattern overflow-hidden">
        <div className="absolute inset-0 purple-gradient opacity-90" />
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h1 className="text-5xl md:text-7xl font-bold mb-8">{t("about.title")}</h1>
          <p className="text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed">
            {t("about.subtitle")}
          </p>
        </div>
      </section>

      {/* Innovative Timeline Section */}
      <section className="py-20 bg-white relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">Our Journey</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From humble beginnings to agricultural innovation - discover the milestones that shaped our story.
            </p>
          </div>

          {/* Timeline */}
          <div className="relative">
            {/* Timeline Line - Desktop: Center, Mobile: Left */}
            <div className="absolute left-4 md:left-1/2 md:transform md:-translate-x-1/2 w-1 h-full bg-gradient-to-b from-purple-500 via-violet-500 to-purple-700 rounded-full"></div>

            <div className="space-y-8 md:space-y-16">
              {timelineEvents.map((event, index) => (
                <div key={index} className="relative">
                  {/* Mobile Layout: Single column with left timeline */}
                  <div className="md:hidden flex items-start">
                    {/* Timeline Node */}
                    <div className="relative z-10 mr-6">
                      <div className={`w-6 h-6 ${event.color} rounded-full border-4 border-white shadow-lg`}></div>
                    </div>

                    {/* Content Card */}
                    <div className="flex-1">
                      <Card className="border-purple-200 hover:shadow-xl transition-all duration-500 hover:scale-105">
                        <CardContent className="p-6">
                          <div className="flex items-center gap-3 mb-4">
                            <div className={`${event.color} p-2 rounded-full`}>
                              <event.icon className="h-5 w-5 text-white" />
                            </div>
                            <div>
                              <div className="text-2xl font-bold text-purple-600">{event.year}</div>
                              <div className="text-lg font-semibold text-gray-800">{event.title}</div>
                            </div>
                          </div>
                          <p className="text-gray-600 leading-relaxed text-sm">{event.description}</p>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  {/* Desktop Layout: Alternating left/right */}
                  <div className={`hidden md:flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                    {/* Content Card */}
                    <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                      <Card className="border-purple-200 hover:shadow-xl transition-all duration-500 hover:scale-105">
                        <CardContent className="p-8">
                          <div className={`flex items-center gap-4 mb-4 ${index % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
                            <div className={`${event.color} p-3 rounded-full`}>
                              <event.icon className="h-6 w-6 text-white" />
                            </div>
                            <div>
                              <div className="text-3xl font-bold text-purple-600">{event.year}</div>
                              <div className="text-xl font-semibold text-gray-800">{event.title}</div>
                            </div>
                          </div>
                          <p className="text-gray-600 leading-relaxed">{event.description}</p>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Timeline Node */}
                    <div className="relative z-10">
                      <div className={`w-6 h-6 ${event.color} rounded-full border-4 border-white shadow-lg`}></div>
                    </div>

                    {/* Spacer */}
                    <div className="w-5/12"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="py-20 bg-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Mission */}
            <Card className="border-purple-200 hover:shadow-xl transition-all duration-300">
              <CardContent className="p-10">
                <div className="flex items-center mb-6">
                  <div className="p-4 bg-purple-100 rounded-full mr-4">
                    <Target className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-800">{t("about.mission")}</h3>
                </div>
                <p className="text-gray-600 text-lg leading-relaxed">
                  At Amber Seeds and Farms, our mission is to provide farmers with world-class onion seeds
                  developed through cutting-edge research, deep agronomic expertise, and decades of field
                  experience. We are committed to quality, consistency, and innovation—ensuring every seed
                  we deliver supports higher yields, better resilience, and long-term farm prosperity.”
      </p>
                <div className="mt-6 space-y-3">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-purple-600" />
                    <span className="text-gray-700">Premium seed quality assurance</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-purple-600" />
                    <span className="text-gray-700">Farmer education and support</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-purple-600" />
                    <span className="text-gray-700">Sustainable agriculture promotion</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Vision */}
            <Card className="border-purple-200 hover:shadow-xl transition-all duration-300">
              <CardContent className="p-10">
                <div className="flex items-center mb-6">
                  <div className="p-4 bg-violet-100 rounded-full mr-4">
                    <Heart className="h-8 w-8 text-violet-600" />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-800">{t("about.vision")}</h3>
                </div>
                <p className="text-gray-600 text-lg leading-relaxed">
                 To be a globally recognized leader in onion seed excellence by delivering world-class quality,
                  advancing agricultural innovation, and empowering farmers through sustainable and
                  reliable solutions.
                  </p>
                <div className="mt-6 space-y-3">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-violet-600" />
                    <span className="text-gray-700">Market leadership in quality</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-violet-600" />
                    <span className="text-gray-700">Innovation in seed technology</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-violet-600" />
                    <span className="text-gray-700">Global agricultural impact</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">{t("about.values")}</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The principles that guide our every decision and drive our commitment to excellence.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center border-purple-200 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <CardContent className="p-8">
                <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-200 transition-colors">
                  <Award className="h-10 w-10 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">{t("values.quality")}</h3>
                <p className="text-gray-600 leading-relaxed">
                  Uncompromising commitment to delivering the highest quality seeds and agricultural solutions.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center border-purple-200 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <CardContent className="p-8">
                <div className="w-20 h-20 bg-violet-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-violet-200 transition-colors">
                  <TrendingUp className="h-10 w-10 text-violet-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">{t("values.innovation")}</h3>
                <p className="text-gray-600 leading-relaxed">
                  Continuous research and development to bring cutting-edge agricultural technologies to farmers.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center border-purple-200 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <CardContent className="p-8">
                <div className="w-20 h-20 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-indigo-200 transition-colors">
                  <Leaf className="h-10 w-10 text-indigo-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">{t("values.sustainability")}</h3>
                <p className="text-gray-600 leading-relaxed">
                  Promoting eco-friendly farming practices for a sustainable and prosperous agricultural future.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center border-purple-200 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <CardContent className="p-8">
                <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-200 transition-colors">
                  <Users className="h-10 w-10 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">{t("values.trust")}</h3>
                <p className="text-gray-600 leading-relaxed">
                  Building lasting relationships with farmers through transparency, reliability, and genuine care.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 purple-pattern relative overflow-hidden">
        <div className="absolute inset-0 purple-gradient opacity-90" />
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h2 className="text-4xl md:text-5xl font-bold mb-8">Ready to Transform Your Farming?</h2>
          <p className="text-xl mb-10 leading-relaxed">
            Join thousands of successful farmers who trust Amber Seeds & Farms for their agricultural needs
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link href="/products">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-purple-50 px-8 py-4 text-lg font-semibold">
                Explore Our Seeds
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-purple-50 px-8 py-4 text-lg font-semibold">
                Contact Our Experts
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
