"use client"

import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Heart, Shield, Leaf, Users, Award, Globe, Star, CheckCircle, ArrowRight, Sparkles } from "lucide-react"
import { useTranslation } from "@/hooks/useTranslation"
import Link from "next/link"

export default function ValuesPage() {
  const { t } = useTranslation()

  const coreValues = [
    {
      icon: Award,
      title: t("values.quality"),
      description: "We maintain the highest standards in seed quality, ensuring every farmer receives premium products that deliver exceptional results.",
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      features: ["95%+ Germination Rate", "Disease Resistance", "Quality Certification", "Regular Testing"]
    },
    {
      icon: Sparkles,
      title: t("values.innovation"),
      description: "Continuous research and development drives our commitment to bringing cutting-edge agricultural technologies to Indian farmers.",
      color: "text-violet-600",
      bgColor: "bg-violet-50",
      borderColor: "border-violet-200",
      features: ["R&D Investment", "Modern Techniques", "Technology Adoption", "Future-Ready Solutions"]
    },
    {
      icon: Leaf,
      title: t("values.sustainability"),
      description: "Environmental stewardship guides our practices, promoting eco-friendly farming for a sustainable agricultural future.",
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
      features: ["Organic Methods", "Water Conservation", "Soil Health", "Eco-Friendly Practices"]
    },
    {
      icon: Users,
      title: t("values.trust"),
      description: "Building lasting relationships with farmers through transparency, reliability, and genuine commitment to their success.",
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
      borderColor: "border-indigo-200",
      features: ["Transparent Pricing", "Expert Support", "Reliable Supply", "Long-term Partnership"]
    }
  ]

  const principles = [
    {
      title: "Farmer-First Approach",
      description: "Every decision we make prioritizes the success and prosperity of our farming partners.",
      icon: Heart
    },
    {
      title: "Continuous Learning",
      description: "We constantly evolve our knowledge and practices to stay ahead in agricultural innovation.",
      icon: Star
    },
    {
      title: "Community Impact",
      description: "Our success is measured by the positive impact we create in farming communities.",
      icon: Globe
    },
    {
      title: "Ethical Business",
      description: "Integrity and honesty form the foundation of all our business relationships.",
      icon: Shield
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="relative py-24 purple-pattern overflow-hidden">
        <div className="absolute inset-0 purple-gradient opacity-90" />
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h1 className="text-5xl md:text-7xl font-bold mb-8">{t("values.title")}</h1>
          <p className="text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed">
            {t("values.subtitle")}
          </p>
        </div>
      </section>

      {/* Core Values - Innovative Card Design */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">Our Core Values</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The fundamental principles that drive our commitment to agricultural excellence and farmer success.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {coreValues.map((value, index) => (
              <Card key={index} className={`${value.borderColor} hover:shadow-2xl transition-all duration-500 hover:scale-105 group overflow-hidden`}>
                <CardContent className="p-0">
                  <div className={`${value.bgColor} p-8 relative`}>
                    <div className="flex items-center mb-6">
                      <div className={`p-4 bg-white rounded-full shadow-lg mr-6 group-hover:scale-110 transition-transform`}>
                        <value.icon className={`h-8 w-8 ${value.color}`} />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-800">{value.title}</h3>
                    </div>
                    <p className="text-gray-700 text-lg leading-relaxed mb-6">{value.description}</p>

                    <div className="grid grid-cols-2 gap-3">
                      {value.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-2">
                          <CheckCircle className={`h-4 w-4 ${value.color}`} />
                          <span className="text-sm text-gray-600">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Guiding Principles */}
      <section className="py-20 bg-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">Guiding Principles</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The foundational beliefs that shape our approach to agriculture and business.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {principles.map((principle, index) => (
              <Card key={index} className="text-center border-purple-200 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
                <CardContent className="p-8">
                  <div className="relative mb-6">
                    <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-violet-600 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform">
                      <principle.icon className="h-10 w-10 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{index + 1}</span>
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-4">{principle.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{principle.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Values in Action */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">Values in Action</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              See how our values translate into real-world impact for farmers and communities.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="border-purple-200 hover:shadow-xl transition-all duration-300">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Star className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">5000+ Farmers Empowered</h3>
                <p className="text-gray-600 leading-relaxed">
                  Through quality seeds and expert guidance, we've helped thousands of farmers achieve better yields and prosperity.
                </p>
              </CardContent>
            </Card>

            <Card className="border-purple-200 hover:shadow-xl transition-all duration-300">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-violet-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Leaf className="h-8 w-8 text-violet-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">Sustainable Practices</h3>
                <p className="text-gray-600 leading-relaxed">
                  Our eco-friendly farming methods have reduced water usage by 30% while maintaining high productivity.
                </p>
              </CardContent>
            </Card>

            <Card className="border-purple-200 hover:shadow-xl transition-all duration-300">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Award className="h-8 w-8 text-indigo-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">Quality Recognition</h3>
                <p className="text-gray-600 leading-relaxed">
                  Certified by leading agricultural bodies for our commitment to quality and sustainable farming practices.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 purple-pattern relative overflow-hidden">
        <div className="absolute inset-0 purple-gradient opacity-90" />
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h2 className="text-4xl md:text-5xl font-bold mb-8">Join Our Mission</h2>
          <p className="text-xl mb-10 leading-relaxed">
            Be part of the agricultural revolution. Experience the difference that values-driven farming can make.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link href="/products">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-purple-50 px-8 py-4 text-lg font-semibold">
                Explore Our Seeds <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-purple-50 px-8 py-4 text-lg font-semibold">
                Partner With Us
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
