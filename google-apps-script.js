/**
 * Google Apps Script for handling user data submissions to Google Sheets
 * 
 * Instructions:
 * 1. Go to https://script.google.com/
 * 2. Create a new project
 * 3. Replace the default code with this script
 * 4. Update the SPREADSHEET_ID with your Google Sheets ID
 * 5. Deploy as a web app with execute permissions for "Anyone"
 * 6. Copy the web app URL and add it to your .env.local file as GOOGLE_SCRIPT_URL
 */
// Replace this with your Google Sheets ID
const SPREADSHEET_ID = '1v58oghosiqYCNBEwGsT8IfvxkL4FXSibNcxsWcFJ14Y';
const SHEET_NAME = 'Customer data'; // The sheet tab name where user data will be stored
function doPost(e) {
  try {
    // Parse the JSON data from the request
    const data = JSON.parse(e.postData.contents);
    
    // Log the received data for debugging
    console.log("Received data:", JSON.stringify(data));
    
    // Open the spreadsheet
    const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);
    
    // Get or create the sheet
    let sheet;
    try {
      sheet = spreadsheet.getSheetByName(SHEET_NAME);
      console.log("Found existing sheet:", SHEET_NAME);
    } catch (error) {
      // If sheet doesn't exist, create it
      console.log("Creating new sheet:", SHEET_NAME);
      sheet = spreadsheet.insertSheet(SHEET_NAME);
      
      // Add headers - make sure these match exactly with your sheet's column names
      const headers = ['Name', 'Address', 'District', 'Contact', 'email', 'Timestamp'];
      
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      
      // Format headers
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setFontWeight('bold');
      headerRange.setBackground('#f0f0f0');
    }
    
    // Prepare the row data - make sure the order matches your headers
    const rowData = [
      data.Name || '',
      data.Address || '',
      data.District || '',
      data.Contact || '',
      data.email || '',
      data.timestamp || new Date().toISOString()
    ];
    
    console.log("Appending row data:", rowData);
    
    // Append the data to the sheet
    sheet.appendRow(rowData);
    
    // Return success response
    return ContentService
      .createTextOutput(JSON.stringify({
        success: true,
        message: 'Data submitted successfully'
      }))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    // Log the error for debugging
    console.error("Error in doPost:", error.toString());
    
    // Return error response
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function doGet(e) {
  // Handle GET requests (for testing)
  return ContentService
    .createTextOutput(JSON.stringify({
      message: 'User Data Collection API is running',
      timestamp: new Date().toISOString()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

// Test function to verify the script works
function testScript() {
  const testData = {
    Name: 'Test User',
    Address: 'Test Address, Test City',
    District: 'Test District',
    Contact: '9876543210',
    email: '<EMAIL>',
    timestamp: new Date().toISOString()
  };
  
  const mockEvent = {
    postData: {
      contents: JSON.stringify(testData)
    }
  };
  
  const result = doPost(mockEvent);
  console.log('Test result:', result.getContent());
}

