"use client"

import type React from "react"

import { useState } from "react"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { MapPin, Phone, Mail, Clock, Send, MessageCircle, Users, Headphones, CheckCircle, PhoneCall, PhoneCallIcon } from "lucide-react"
import { useTranslation } from "@/hooks/useTranslation"

export default function ContactPage() {
  const { t } = useTranslation()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))

    console.log("Form submitted:", formData)
    setIsSubmitted(true)
    setIsSubmitting(false)

    // Reset form after 3 seconds
    setTimeout(() => {
      setFormData({
        name: "",
        email: "",
        phone: "",
        subject: "",
        message: "",
      })
      setIsSubmitted(false)
    }, 3000)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const contactInfo = [
    {
      icon: MapPin,
      title: t("contact.address"),
      details: ["Pranayraj, Sarojini Devi Road ", "Jalna, Maharashtra, Bharat"],
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      icon: Phone,
      title: t("contact.phone"),
      details: ["+91 88054 92999"],
      color: "text-violet-600",
      bgColor: "bg-violet-50"
    },
    {
      icon: Mail,
      title: t("contact.email"),
      details: ["<EMAIL>"],
      color: "text-indigo-600",
      bgColor: "bg-indigo-50"
    },
    {
      icon: Clock,
      title: "Business Hours",
      details: ["Monday to Saturday – 9:00 AM – 5:00 PM .(We are closed on Sunday)"],
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
  ]

  const supportOptions = [
    {
      icon: PhoneCall,
      title: "Expert Consultation",
      description: "Get personalised solutions from our trusted agricultural experts",
      action: "Schedule Call",
      onClick: () => window.open('tel:8805492999', '_self')
    },
    {
      icon: Users,
      title: "Farmer Community",
      description: "Join our farmer community to turn every season into a success story",
      action: "Join Community",
      onClick: () => window.open('https://chat.whatsapp.com/L5XeibLyx2hHFQViyFsI0j?mode=ac_t', '_blank')
    },
    {
      icon: MessageCircle,
      title: "Technical Support",
      description: "Get instant WhatsApp support for all your farming queries",
      action: "Get Support",
      onClick: () => window.open('https://wa.me/918805492999', '_blank')
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="relative py-24 purple-pattern overflow-hidden">
        <div className="absolute inset-0 purple-gradient opacity-90" />
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h1 className="text-5xl md:text-7xl font-bold mb-8">{t("contact.title")}</h1>
          <p className="text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed">
            {t("contact.subtitle")}
          </p>
        </div>
      </section>

      {/* Support Options */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">How Can We Help?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose the best way to connect with our team of agricultural experts.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {supportOptions.map((option, index) => (
              <Card key={index} className="border-purple-200 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
                <CardContent className="p-8 text-center">
                  <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-200 transition-colors">
                    <option.icon className="h-10 w-10 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-4">{option.title}</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{option.description}</p>
                  <Button
                    className="bg-purple-600 hover:bg-purple-700 text-white"
                    onClick={option.onClick}
                  >
                    {option.action}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-20 bg-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">Get In Touch</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Multiple ways to reach our team of agricultural experts.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contactInfo.map((info, index) => (
              <Card key={index} className={`flex flex-col items-center border-purple-200 hover:shadow-xl transition-all duration-300 hover:scale-105 group`}>
                <CardContent className="p-8 text-center">
                  <div className={`w-16 h-16 ${info.bgColor} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform`}>
                    <info.icon className={`h-8 w-8 ${info.color}`} />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-4">{info.title}</h3>
                  <div className="space-y-3">
                    {info.details.map((detail, idx) => (
                      <div key={idx} className="text-gray-600">
                        {detail.includes("@") ? (
                          <div className="flex flex-col space-y-1">
                            <a
                              href={`mailto:${detail}`}
                              className="text-purple-600 hover:text-purple-800 font-medium transition-all duration-200 hover:underline inline-flex items-center group"
                            >
                              <Mail className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                              {detail}
                            </a>
                            <span className="text-xs text-gray-500 ml-6">Click to send email</span>
                          </div>
                        ) : detail.includes("+91") || detail.includes("phone") || detail.includes("Phone") ? (
                          <div className="flex flex-col space-y-1">
                            <a
                              href={`tel:${detail.replace(/\s/g, '')}`}
                              className="text-purple-600 hover:text-purple-800 font-medium transition-all duration-200 hover:underline inline-flex items-center group"
                            >
                              <Phone className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                              {detail}
                            </a>
                            <span className="text-xs text-gray-500 ml-6">Click to call</span>
                          </div>
                        ) : (
                          <div className="flex items-start">
                            <span className="text-gray-700 leading-relaxed">{detail}</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form */}
      {/* <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">Send Us a Message</h2>
            <p className="text-xl text-gray-600">
              Fill out the form below and we'll get back to you within 24 hours.
            </p>
          </div>

          <Card className="border-purple-200 shadow-2xl">
            <CardContent className="p-10">
              {isSubmitted ? (
                <div className="text-center py-12">
                  <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <CheckCircle className="h-10 w-10 text-green-600" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">Message Sent Successfully!</h3>
                  <p className="text-gray-600">Thank you for contacting us. We'll get back to you soon.</p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name *
                      </label>
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        required
                        value={formData.name}
                        onChange={handleChange}
                        className="border-purple-300 focus:border-purple-500 focus:ring-purple-500 h-12"
                        placeholder="Enter your full name"
                        disabled={isSubmitting}
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address *
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        required
                        value={formData.email}
                        onChange={handleChange}
                        className="border-purple-300 focus:border-purple-500 focus:ring-purple-500 h-12"
                        placeholder="Enter your email address"
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={handleChange}
                        className="border-purple-300 focus:border-purple-500 focus:ring-purple-500 h-12"
                        placeholder="Enter your phone number"
                        disabled={isSubmitting}
                      />
                    </div>

                    <div>
                      <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                        Subject *
                      </label>
                      <Input
                        id="subject"
                        name="subject"
                        type="text"
                        required
                        value={formData.subject}
                        onChange={handleChange}
                        className="border-purple-300 focus:border-purple-500 focus:ring-purple-500 h-12"
                        placeholder="Enter message subject"
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                      Message *
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      required
                      rows={6}
                      value={formData.message}
                      onChange={handleChange}
                      className="border-purple-300 focus:border-purple-500 focus:ring-purple-500"
                      placeholder="Tell us about your farming needs, questions, or how we can help you..."
                      disabled={isSubmitting}
                    />
                  </div>

                  <div className="text-center">
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="bg-purple-600 hover:bg-purple-700 text-white px-12 py-4 text-lg font-semibold disabled:opacity-50"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className="h-5 w-5 mr-2" />
                          Send Message
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              )}
            </CardContent>
          </Card>
        </div>
      </section> */}

      {/* Map Section */}
      <section className="py-20 bg-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">Visit Us</h2>
            <p className="text-xl text-gray-600">
              Experience our agricultural excellence firsthand at our state-of-the-art facility.
            </p>
          </div>

          <Card className="border-purple-200 overflow-hidden shadow-2xl">
            <CardContent className="p-0">
              <div className="aspect-video relative">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3785.123456789!2d75.88123456789!3d19.83456789!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bdb98765432:0x123456789abcdef!2sPranayraj%2C%20Sarojini%20Devi%20Road%2C%20Jalna%2C%20Maharashtra%20431203!5e0!3m2!1sen!2sin!4v1640995200000!5m2!1sen!2sin"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Amber Seeds & Farms Location - Pranayraj, Sarojini Devi Road, Jalna"
                  className="absolute inset-0"
                ></iframe>

                {/* Alternative: Direct link to Google Maps */}
                <div className="absolute top-4 right-4">
                  <a
                    href="https://maps.app.goo.gl/KGkPpfJhUm8qERyi9"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-lg hover:bg-white transition-colors"
                    title="Open in Google Maps"
                  >
                    <MapPin className="h-5 w-5 text-purple-600" />
                  </a>
                </div>

                {/* Address overlay */}
                <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg p-4 shadow-lg max-w-sm">
                  <div className="flex items-start space-x-3">
                    <MapPin className="h-6 w-6 text-purple-600 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-1">Amber Seeds & Farms</h4>
                      <p className="text-sm text-gray-600">
                        Pranayraj, Sarojini Devi Road<br />
                        Jalna – 431203, Maharashtra, India
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  )
}
