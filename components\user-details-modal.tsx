"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { User, MapPin, Phone, Mail } from "lucide-react"

interface UserData {
  fullName: string
  address: string
  district: string
  contactNo: string
  email: string
  city: string
}

interface UserDetailsModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function UserDetailsModal({ isOpen, onClose }: UserDetailsModalProps) {
  const [formData, setFormData] = useState<UserData>({
    fullName: "",
    address: "",
    district: "",
    contactNo: "",
    email: "",
    city: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (field: keyof UserData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateForm = () => {
    if (!formData.fullName.trim()) {
      alert("Please enter your full name")
      return false
    }
    if (!formData.address.trim()) {
      alert("Please enter your address")
      return false
    }
    if (!formData.district.trim()) {
      alert("Please enter your district")
      return false
    }
    if (!formData.contactNo.trim()) {
      alert("Please enter your contact number")
      return false
    }
    if (!formData.city.trim()) {
      alert("Please enter your city")
      return false
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // Google Form submission using the correct form ID
      const FORM_ID = '1FAIpQLSe3DDh7QXUmmSUwJ_ii4lJ8v6nL9HdbHKUfR_NSbg6tDw-k2w';
      // Entry IDs from your Google Form
      const FULL_NAME_ENTRY_ID = '2067754775';
      const ADDRESS_ENTRY_ID = '739027507';
      const DISTRICT_ENTRY_ID = '641450138';
      const CONTACT_ENTRY_ID = '1005004665';
      const EMAIL_ENTRY_ID = '356632283';
      const CITY_ENTRY_ID = '1499898154';

      // Build the form submission URL with URL parameters
      const formUrl = new URL(`https://docs.google.com/forms/d/e/${FORM_ID}/formResponse`);

      // Add form data as URL parameters
      formUrl.searchParams.append(`entry.${FULL_NAME_ENTRY_ID}`, formData.fullName);
      formUrl.searchParams.append(`entry.${ADDRESS_ENTRY_ID}`, formData.address);
      formUrl.searchParams.append(`entry.${DISTRICT_ENTRY_ID}`, formData.district);
      formUrl.searchParams.append(`entry.${CONTACT_ENTRY_ID}`, formData.contactNo);
      formUrl.searchParams.append(`entry.${EMAIL_ENTRY_ID}`, formData.email);
      formUrl.searchParams.append(`entry.${CITY_ENTRY_ID}`, formData.city);

      console.log("Submitting to Google Form:", formUrl.toString());

      // Use a hidden iframe to submit the form without navigating away
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      document.body.appendChild(iframe);

      // Set up a listener to know when the iframe has loaded
      iframe.onload = () => {
        document.body.removeChild(iframe);

        onClose();

        // Reset form
        setFormData({
          fullName: "",
          address: "",
          district: "",
          contactNo: "",
          email: "",
          city: "",
        });

        setIsSubmitting(false);
      };

      // Submit the form by setting the iframe's src to the form URL
      iframe.src = formUrl.toString();

    } catch (error) {
      console.error('Error submitting data:', error);
      onClose();
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-purple-800 flex items-center gap-2">
            <User className="h-6 w-6" />
            Welcome to Amber Seeds
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Please provide your details to access product information and receive updates about our premium onion varieties.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 mt-6">
          <div className="space-y-2">
            <Label htmlFor="fullName" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <User className="h-4 w-4 text-purple-600" />
              Full Name *
            </Label>
            <Input
              id="fullName"
              type="text"
              placeholder="Enter your full name"
              value={formData.fullName}
              onChange={(e) => handleInputChange("fullName", e.target.value)}
              className="border-purple-200 focus:border-purple-500 focus:ring-purple-500"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="address" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <MapPin className="h-4 w-4 text-purple-600" />
              Address *
            </Label>
            <Input
              id="address"
              type="text"
              placeholder="Enter your address"
              value={formData.address}
              onChange={(e) => handleInputChange("address", e.target.value)}
              className="border-purple-200 focus:border-purple-500 focus:ring-purple-500"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="city" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <MapPin className="h-4 w-4 text-purple-600" />
              City *
            </Label>
            <Input
              id="city"
              type="text"
              placeholder="Enter your city"
              value={formData.city}
              onChange={(e) => handleInputChange("city", e.target.value)}
              className="border-purple-200 focus:border-purple-500 focus:ring-purple-500"
              required
            />
          </div>


          <div className="space-y-2">
            <Label htmlFor="district" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <MapPin className="h-4 w-4 text-purple-600" />
              District *
            </Label>
            <Input
              id="district"
              type="text"
              placeholder="Enter your district"
              value={formData.district}
              onChange={(e) => handleInputChange("district", e.target.value)}
              className="border-purple-200 focus:border-purple-500 focus:ring-purple-500"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="contactNo" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Phone className="h-4 w-4 text-purple-600" />
              Contact No. *
            </Label>
            <Input
              id="contactNo"
              type="tel"
              placeholder="Enter your contact number"
              value={formData.contactNo}
              onChange={(e) => handleInputChange("contactNo", e.target.value.replace(/\D/g, '').slice(0, 10))}
              className="border-purple-200 focus:border-purple-500 focus:ring-purple-500"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Mail className="h-4 w-4 text-purple-600" />
              Email
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email address (optional)"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              className="border-purple-200 focus:border-purple-500 focus:ring-purple-500"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1 border-purple-200 text-purple-700 hover:bg-purple-50"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Submitting..." : "Submit & Continue"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
