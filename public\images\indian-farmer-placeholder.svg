<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="fieldGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#22C55E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#15803D;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Sky background -->
  <rect width="800" height="300" fill="url(#skyGradient)"/>
  
  <!-- Field background -->
  <rect y="300" width="800" height="300" fill="url(#fieldGradient)"/>
  
  <!-- Sun -->
  <circle cx="650" cy="100" r="40" fill="#FBBF24" opacity="0.8"/>
  
  <!-- Mountains in background -->
  <polygon points="0,250 150,150 300,200 450,120 600,180 800,160 800,300 0,300" fill="#92400E" opacity="0.6"/>
  
  <!-- Onion field rows -->
  <g stroke="#15803D" stroke-width="2" opacity="0.7">
    <line x1="0" y1="350" x2="800" y2="340"/>
    <line x1="0" y1="380" x2="800" y2="370"/>
    <line x1="0" y1="410" x2="800" y2="400"/>
    <line x1="0" y1="440" x2="800" y2="430"/>
    <line x1="0" y1="470" x2="800" y2="460"/>
    <line x1="0" y1="500" x2="800" y2="490"/>
  </g>
  
  <!-- Onion plants -->
  <g fill="#16A34A">
    <circle cx="100" cy="360" r="3"/>
    <circle cx="150" cy="358" r="3"/>
    <circle cx="200" cy="356" r="3"/>
    <circle cx="250" cy="354" r="3"/>
    <circle cx="300" cy="352" r="3"/>
    <circle cx="350" cy="350" r="3"/>
    <circle cx="400" cy="348" r="3"/>
    <circle cx="450" cy="346" r="3"/>
    <circle cx="500" cy="344" r="3"/>
    <circle cx="550" cy="342" r="3"/>
    <circle cx="600" cy="340" r="3"/>
    <circle cx="650" cy="338" r="3"/>
    <circle cx="700" cy="336" r="3"/>
  </g>
  
  <!-- Farmer silhouette -->
  <g fill="#78350F">
    <!-- Body -->
    <ellipse cx="400" cy="450" rx="25" ry="60"/>
    <!-- Head -->
    <circle cx="400" cy="380" r="20"/>
    <!-- Turban -->
    <ellipse cx="400" cy="375" rx="22" ry="15" fill="#F59E0B"/>
    <!-- Arms -->
    <ellipse cx="380" cy="420" rx="8" ry="30" transform="rotate(-20 380 420)"/>
    <ellipse cx="420" cy="420" rx="8" ry="30" transform="rotate(20 420 420)"/>
    <!-- Legs -->
    <ellipse cx="390" cy="500" rx="8" ry="35"/>
    <ellipse cx="410" cy="500" rx="8" ry="35"/>
  </g>
  
  <!-- Traditional farming tool (hoe) -->
  <g stroke="#92400E" stroke-width="3" fill="none">
    <line x1="430" y1="400" x2="450" y2="380"/>
    <line x1="445" y1="375" x2="455" y2="385"/>
  </g>
  
  <!-- Traditional basket -->
  <ellipse cx="350" cy="480" rx="15" ry="8" fill="#92400E" opacity="0.8"/>
  
  <!-- Text overlay -->
  <text x="400" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#78350F">
    Indian Farmer - Onion Cultivation
  </text>
  
  <!-- Decorative border -->
  <rect x="10" y="10" width="780" height="580" fill="none" stroke="#F59E0B" stroke-width="4" rx="10"/>
</svg>
